import { defineConfig } from "wxt";
// import { Plugin } from 'vite';
import tailwindcss from "@tailwindcss/vite";
import svgr from "vite-plugin-svgr";

// See https://wxt.dev/api/config.html
export default defineConfig({
  modules: ["@wxt-dev/module-react"],
  srcDir: "src",
  manifest: {
    name: "Adq 广告投放助手",
    icons: {
      16: "icon.png",
      32: "icon.png",
      48: "icon.png",
      128: "icon.png",
    },
    permissions: ["activeTab", "tabs", "cookies", "scripting", "storage"],
    host_permissions: ["<all_urls>"],
    web_accessible_resources: [
      {
        resources: ["/inject.js"],
        matches: ["*://*/*"],
      }
    ]
  },
  webExt: {
    disabled: true,
  },
  vite: () => ({
    plugins: [tailwindcss(), svgr() as Plugin],
    resolve: {
      alias: {
        "@": "/src",
      },
    },
  }),
});
