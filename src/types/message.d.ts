// // 基础消息类型
// export interface BaseMessage<T = any> {
//   type: string;
//   data?: T;
//   error?: string;
// }

// // 消息类型枚举
// export enum MessageType {
//   // Background与Popup通信
//   POPUP_INIT = 'popup_init',
//   POPUP_READY = 'popup_ready',
  
//   // Background与Content通信 
//   CONTENT_INIT = 'content_init',
//   CONTENT_READY = 'content_ready',
  
//   // Content与Main World通信
//   MAIN_WORLD_INIT = 'main_world_init',
//   MAIN_WORLD_READY = 'main_world_ready',
  
//   // 业务相关消息
//   GET_CONFIG = 'get_config',
//   SET_CONFIG = 'set_config',
//   GET_USER_INFO = 'get_user_info',
//   // 其他业务消息...
// }

// // 消息处理器类型
// export type MessageHandler = (
//   message: BaseMessage,
//   sender: chrome.runtime.MessageSender,
//   sendResponse: (response?: any) => void
// ) => void | Promise<void>;
