import { LogData } from "@/components/LogView"

export type Base = {
  accounts?: Map<string, Account>
  canUse?: boolean
  message?: string
  current?: Account
}

export type Source = {
  account_id: number
  headers: any
  logData: LogData
  current: Base["current"]
  task: {
    uuid: string
    taskId?: number
  }
}

export type MessageToBackgroundBody = Pick<Source, "current" | "task" | "account_id"> & {data:any}