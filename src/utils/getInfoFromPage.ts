export const getSelectedCreativeIdsFromListPage = () => {
  const $tr = document.querySelectorAll(
    "tbody tr:has(.spaui-checkbox-checked)"
  )
  if (!$tr?.length) return null
  try {
    const creativeIds = Array.from($tr).map((tr) => {
      const $update = tr.querySelector(
        '[data-hottag="Click.Function.worktable.ad.action.edit"]'
      ) as any
      if (!$update) {
        throw new Error("未获取到选中创意或不在创意列表页面")
      }
      const href = $update.href
      // https://ad.qq.com/atlas/51460172/addelivery/creatives-update?adgroup_id=26078878025&dynamic_creative_id=1903352879
      const searchParams = new URLSearchParams(href.split("?")[1])
      const creativeId = searchParams.get("dynamic_creative_id")
      if (!creativeId) {
        throw new Error("未获取到选中创意或不在创意列表页面")
      }
      return creativeId
    })
    return creativeIds
  } catch {
    return null
  }
}

export const getSelectedAdgroundIdsFromListPage = () => {
  const $tr = document.querySelectorAll(
    "tbody tr:has(.spaui-checkbox-checked)"
  )
  if (!$tr?.length) return null
  try {
    const adgroupIds = Array.from($tr).map((tr) => {
      const $update = tr.querySelector(
        '[data-hottag="Click.Function.worktable.adgroup.action.edit"]'
      ) as any
      if (!$update) {
        throw new Error("未获取到选中广告或不在广告列表页面")
      }
      const href = $update.href
      // https://ad.qq.com/atlas/51460172/addelivery/creatives-update?adgroup_id=26078878025&dynamic_creative_id=1903352879
      const searchParams = new URLSearchParams(href.split("?")[1])
      const adgroupId = searchParams.get("adgroup_id")
      if (!adgroupId) {
        throw new Error("未获取到选中广告或不在广告列表页面")
      }
      return adgroupId
    })
    return adgroupIds
  } catch {
    return null
  }
}