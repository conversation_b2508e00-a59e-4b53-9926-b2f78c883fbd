import { nanoid } from "nanoid/non-secure";
import type { Source } from "@/types/source";
import { sendMessage } from "@/message";
import { LogData } from "@/components/LogView";

export const createSource = async ({ logData }: {logData: LogData}): Promise<Source> => {
  const headers = await sendMessage("adq_get_tencent_headers", {});
  const currentAccount = (await sendMessage(
    "adq_get_current_account",
    {}
  )) as any;
  const account_id = Number(currentAccount?.account_id);
  const uuid = nanoid(10);

  const source: Source = {
    task: { uuid },
    headers,
    logData,
    account_id,
    current: currentAccount,
  };
  return source;
};
