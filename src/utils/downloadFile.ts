export async function getArrayBuffurFromRemoteUrl(url: string):Promise<{
  fileName: string | null;
  arrayBuffer: ArrayBuffer | null;
  message?: any
}> {
  try {
    // 1. 发起网络请求获取文件
    console.log("开始下载文件:", url);
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }

    // 2. 尝试从响应头中获取文件名
    let fileName: string = getFileNameFromResponse(response) as string;

    // 3. 如果没有从响应头获取到文件名，则生成默认名称
    if (!fileName) {
      // 从URL提取可能的文件名部分
      const urlPath = new URL(url).pathname;
      const potentialName = urlPath.split("/").pop();

      // 使用时间戳生成唯一文件名
      fileName = potentialName || `download_${Date.now()}`;

      // 尝试从Content-Type推断文件扩展名
      const contentType = response.headers.get("content-type");
      if (contentType) {
        const extension = getExtensionFromMimeType(contentType);
        if (extension) fileName += `.${extension}`;
      }
    }
    const buffer = await response.arrayBuffer()

    return { fileName, arrayBuffer:  buffer};
  } catch (error) {
    console.error("下载文件时出错:", error);
    // throw error;
    return { fileName: null, arrayBuffer: null,  message: error }
  }
}

// 辅助函数：从响应头中提取文件名
function getFileNameFromResponse(response: Response) {
  const contentDisposition = response.headers.get("content-disposition");
  if (!contentDisposition) return null;

  // 解析 Content-Disposition 头
  const matches = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/.exec(
    contentDisposition
  );
  if (matches && matches[1]) {
    return matches[1].replace(/['"]/g, "");
  }
  return null;
}

// 辅助函数：根据 MIME 类型推断文件扩展名
function getExtensionFromMimeType(mimeType: string) {
  const extensionsMap = {
    "image/jpeg": "jpg",
    "image/png": "png",
    "image/gif": "gif",
    "image/webp": "webp",
    "application/pdf": "pdf",
    "application/zip": "zip",
    "text/plain": "txt",
    "application/json": "json",
    "video/mp4": "mp4",
    "audio/mpeg": "mp3",
    // 可根据需要扩展更多类型
  };

  return extensionsMap[mimeType as keyof typeof extensionsMap] || null;
}
