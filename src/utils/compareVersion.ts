export function compareVersions(version1: string, version2: string) {
  const parseVersion = (version: string) => {
    return version.split(".").map((part) => {
      // 分离版本号中的数字部分和预发布标签
      const [number, preRelease] = part.split("-");
      return {
        number: parseInt(number, 10),
        preRelease: preRelease || "",
      };
    });
  };

  const v1Parts = parseVersion(version1);
  const v2Parts = parseVersion(version2);

  const maxLength = Math.max(v1Parts.length, v2Parts.length);

  for (let i = 0; i < maxLength; i++) {
    const v1 = v1Parts[i] || { number: 0, preRelease: "" };
    const v2 = v2Parts[i] || { number: 0, preRelease: "" };

    if (v1.number > v2.number) {
      return 1;
    }
    if (v1.number < v2.number) {
      return -1;
    }

    // 如果数字部分相同，比较预发布标签
    if (v1.preRelease > v2.preRelease) {
      return 1;
    }
    if (v1.preRelease < v2.preRelease) {
      return -1;
    }
  }

  return 0;
}

// 用法示例
// console.log(compareVersions('1.2.3', '1.2.3'));       // 0
// console.log(compareVersions('1.2.3', '1.3.0'));       // -1
// console.log(compareVersions('1.3.0', '1.2.9'));       // 1
// console.log(compareVersions('1.2.3-alpha', '1.2.3')); // -1
// console.log(compareVersions('1.2.3', '1.2.3-beta'));  // 1
