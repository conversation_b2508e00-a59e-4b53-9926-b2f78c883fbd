export const getSoJdTencentLandings = async (source) => {
  console.log("get_so_jd_tencent_landing");

  let promise:PromiseWithResolvers<any> | null = Promise.withResolvers();

  const originUrls = source.map((item) => item.originUrl);
  await storage.setItem("local:so_jd_tencent_landing_origin_urls", originUrls);

  window.addEventListener("message", (event) => {
    if (event.data?.type === "get_so_jd_tencent_landing_result") {
      const { success, message, modelMap } = event.data.data;
      const _data = source.map((item) => {
        const so = modelMap.list?.find((so) => so.targetUrl === item.originUrl);
        return {
          ...item,
          so,
        };
      });

      promise!.resolve(_data);
    }
  });

  const childWindow = window.open(
    "https://so.jd.com/systemSetting-page/linkTool/landingPage",
    "_blank"
  );
  // 每次打开新窗口后等待一段时间，避免浏览器拦截
  const result = await promise.promise;

  promise = null;
  childWindow?.close();

  window.postMessage(
    {
      type: "ADQ_TO_WEBPAGE_KOOLER",
      action: "get_so_jd_tencent_landing_result",
      data: result,
    },
    "*"
  );

  // browser.tabs.create({
  //   url: `https://so.jd.com/systemSetting-page/linkTool/landingPage`,
  // });
};
