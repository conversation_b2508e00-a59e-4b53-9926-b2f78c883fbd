import { sendMessage } from "@/message";

export const getJDCategoryWithKooler = async (source: any[]) => {
  let promise: PromiseWithResolvers<any> | null = null;
  let result = source;
  const cache = {}

  const handleMessage = (event: MessageEvent) => {
    if (event.data?.type === "jd_category_result") {
      const { success, category, name, error, url, id } = event.data.data;
      const categoryArr = category?.split(">") || [];
      const index = source.findIndex((item) => item.id == id);
      result[index] = {
        ...result[index],
        category1: categoryArr[0] ?? "",
        category2: categoryArr[1] ?? "",
        category3: categoryArr[2] ?? "",
        categoryStatus: success ? "success" : "error",
        categoryMessage: success ? undefined : error || "获取分类失败",
      };
      cache[id] = {
        category1: categoryArr[0] ?? "",
        category2: categoryArr[1] ?? "",
        category3: categoryArr[2] ?? "",
        categoryStatus: success ? "success" : "error",
        categoryMessage: success ? undefined : error || "获取分类失败",
      };
      promise!.resolve(result);
    }
  };

  window.addEventListener("message", handleMessage);

  try {
    const tabId = await sendMessage('adq_get_tab_id', {}, );
    for (const item of result) {
      if(cache[item.id]) {
        item.category1 = cache[item.id].category1;
        item.category2 = cache[item.id].category2;
        item.category3 = cache[item.id].category3;
        item.categoryStatus = cache[item.id].categoryStatus;
        item.categoryMessage = cache[item.id].categoryMessage;
        continue;
      }
      promise = Promise.withResolvers();
      const previewUrl = `https://item.jd.com/${item.id}.html?adq_preview=1`;
      const childWindow = window.open(previewUrl, "_blank");
      // 每次打开新窗口后等待一段时间，避免浏览器拦截
      result = (await promise.promise) as any;
      promise = null;
      childWindow?.close();
    }
    console.log(result)
    await sendMessage('adq_set_active_tab', { tabId: tabId });
  } catch (error) {
    // message.error("查询商品分类时发生错误");
  } finally {
    // setLoading(false);
    // 运行完成后将当前标签页激活
    // sendMessage("adq_set_active_tab", { tabId: currentTabId });
    window.postMessage({
      type: "ADQ_TO_WEBPAGE_KOOLER",
      action: "get_jd_category_result",
      data: result,
    }, "*"); 
  }
};
