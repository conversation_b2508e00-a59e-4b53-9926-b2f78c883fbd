import "@/assets/styles/tailwind.css"
import { getJDCategoryWithKooler } from "./getJDCategory";
import { getSoJdTencentLandings } from "./getSoLanding";

export default defineContentScript({
  // matches: ['<all_urls>'],
  matches: ['<all_urls>'],
  cssInjectionMode: "manifest",
  async main(ctx) {
    console.log(12132312, "kooler content script loaded");
    window.addEventListener("message", (event) => {
      // 只处理来自当前页面的消息
      if (event.source !== window) return;
      
      if (event.data.type && event.data.type === "ADQ_FROM_WEBPAGE_KOOLER") {
        switch (event.data.action) {
          case 'get_jd_category':
            getJDCategoryWithKooler(event.data.data)
            break;
          case 'get_so_jd_tencent_landing':
            getSoJdTencentLandings(event.data.data)
            break;
        }
      }
    });  
  },
});
