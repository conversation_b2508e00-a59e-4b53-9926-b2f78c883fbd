import { useState, useEffect, useRef } from "react";
import Logo from "@/assets/icon.png";
import { Button, message, Space, Tag, Tooltip } from "antd";
import {
  AlertOutlined,
  CaretRightOutlined,
  EllipsisOutlined,
  HomeOutlined,
  InfoCircleFilled,
  InfoCircleOutlined,
} from "@ant-design/icons";
import { Input } from "@/components/ui/input";
import { features } from "./features";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import PinSvg from "@/assets/svg/pin.svg?react";
import PinCancelSvg from "@/assets/svg/pin_cancel.svg?react";
import { storage } from "#imports";
import RefreshSvg from "@/assets/svg/refresh.svg?react";
import { Toaster } from "@/components/ui/sonner";
import { toast } from "sonner";
import { sendMessage } from "@/message";
import "@/assets/styles/tailwind.css";

enum PlatformEnum {
  TencentAd = "腾讯广告平台",
  Reyun = "热云",
  Jd = "京东",
  CL = "创量",
}
const antdColorforPlatformMap = {
  腾讯: "processing",
  热云: "green",
  京东: "volcano",
  创量: "cyan",
};

const PIN_FEATURE_KEY_STORAGE_KEY = "local:adq_pin_feature_key";

function App(props) {
  const [platform, setPlatform] = useState<PlatformEnum>(
    PlatformEnum.TencentAd
  );
  const [_features, setFeatures] = useState<typeof features>(features);
  const pinFeatureKeysRef = useRef<Set<string>>(new Set());
  const [keyword, setKeyword] = useState("");
  const [connectLoading, setConnectLoading] = useState(false);
  const [account, setAccount] = useState<AdqResult<Account>>();

  const { is_content_script, onHide } = props;

  useEffect(() => {
    sendMessage("adq_build_connect", {}).then((data: any) => {
      setAccount(data);
    });
    if (!is_content_script) {
      browser.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
        matchPlatform(tab.url!);
      });
      browser.tabs.query({ active: true, currentWindow: true }, (tabs) => {
        matchPlatform(tabs[0].url!);
      });
    } else {
      matchPlatform(window.location.href);
    }
  }, []);

  useEffect(() => {
    storage
      .getItem<string[]>(PIN_FEATURE_KEY_STORAGE_KEY, { fallback: [] })
      .then((pinFeatureKeys) => {
        pinFeatureKeysRef.current = new Set(pinFeatureKeys || []);
        handleUpdatePin();
      });
  }, []);

  const matchPlatform = (url: string) => {
    const urlObj = new URL(url);
    if (/ad\.qq\.com/.test(urlObj.hostname))
      setPlatform(PlatformEnum.TencentAd);
    if (/data\.insightrackr\.com/.test(urlObj.hostname))
      setPlatform(PlatformEnum.Reyun);
    if (/jd\.com/.test(urlObj.hostname)) setPlatform(PlatformEnum.Jd);
  };

  const handleUpdatePin = () => {
    const newFeatures: any = [];
    // 按照置顶顺序重新将_features排序
    pinFeatureKeysRef.current.forEach((item) => {
      const feature = features.find((_item) => _item.key === item);
      if (feature) newFeatures.push({ ...feature, pin: true });
    });
    features
      .filter((item) => !pinFeatureKeysRef.current.has(item.key))
      .forEach((item) => newFeatures.push(item));
    setFeatures(newFeatures);
    storage.setItem(PIN_FEATURE_KEY_STORAGE_KEY, [
      ...pinFeatureKeysRef.current,
    ]);
  };

  const setPinKey = async (key: string) => {
    if (pinFeatureKeysRef.current.has(key)) {
      pinFeatureKeysRef.current.delete(key);
    } else {
      pinFeatureKeysRef.current = new Set([key, ...pinFeatureKeysRef.current]);
    }
    handleUpdatePin();
  };

  const handleChangeKeyword = (value) => {
    setKeyword(value);
    setFeatures(
      features.filter(
        (feature) =>
          feature.label.includes(value) || feature.platform.includes(value)
      )
    );
  };

  const handleConnect = async () => {
    setConnectLoading(true);
    await sendMessage("adq_build_connect", { force: true }).then(
      (data: any) => {
        setAccount(data);
      }
    );
    toast("刷新完成");
    setConnectLoading(false);
  };

  const handleExec = async (feature: (typeof features)[0]) => {
    if (!is_content_script) {
      const tab = await browser.tabs.query({
        active: true,
        currentWindow: true,
      });
      const url = new URL(tab[0].url!);
      if (feature.platform == "腾讯" && !url.hostname.includes("ad.qq.com"))
        return browser.tabs.create({ url: "https://ad.qq.com" });
      if (
        feature.platform == "热云" &&
        !url.hostname.includes("data.insightrackr.com")
      )
        return browser.tabs.create({ url: "https://data.insightrackr.com" });

      if (feature.href) return browser.tabs.create({ url: feature.href });

      if (feature.platform === account?.data?.platform && !account?.success)
        return toast.error(account?.message);

      if (feature.action) {
        const tab = await browser.tabs.query({
          active: true,
          currentWindow: true,
        });
        sendMessage(feature.action as any, null, tab[0].id);
      }
    } else {
      const url = new URL(window.location.href);
      if (feature.platform == "腾讯" && !url.hostname.includes("ad.qq.com"))
        return window.open("https://ad.qq.com");
      if (
        feature.platform == "热云" &&
        !url.hostname.includes("data.insightrackr.com")
      )
        return window.open("https://data.insightrackr.com/creative/material");

      if (feature.platform === account?.data?.platform && !account?.success)
        return toast.error(account?.message);

      if (feature.action) {
        sendMessage("forwardContent2Content", {
          action: feature.action,
          params: null,
        });
      }
      onHide?.();
    }
  };

  return (
    <div
      className={`flex flex-col relative bg-gray-100 ${
        is_content_script ? "w-full h-full" : "w-[360px] h-[600px]"
      }`}
    >
      <div className="absolute left-0 top-0 w-full h-[40vh] bg-black rounded-b-4xl"></div>
      <div className="relative flex flex-col h-full">
        <header className="">
          <div className="flex justify-between text-white text-xl px-4 pt-6 font-medium">
            <h1 className="flex gap-2 items-center">
              <img src={Logo} className="w-6 h-6" />
              <span>ADQ广告助手</span>
              <Button
                type="link"
                 className="text-[12px] self-end-safe !p-0 !items-end"
                onClick={() =>
                  is_content_script
                    ? window.open(
                        "https://doc.weixin.qq.com/smartsheet/s3_AFEAugaLAKcCNga3HJC7lROqyCxgx?scode=AAUAFAedAAoI0AbWTlAFEAugaLAKc&tab=q979lj&viewId=vukaF8"
                      )
                    : browser.tabs.create({
                        url: "https://doc.weixin.qq.com/smartsheet/s3_AFEAugaLAKcCNga3HJC7lROqyCxgx?scode=AAUAFAedAAoI0AbWTlAFEAugaLAKc&tab=q979lj&viewId=vukaF8",
                      })
                }
              >{browser.runtime.getManifest().version}</Button>
              {/* <span className="text-[12px] self-end-safe">
                {browser.runtime.getManifest().version}
              </span> */}
            </h1>
            <div className="flex gap-1 text-white">
              <Tooltip title="刷新连接">
                <div
                  className="w-8 h-8 bg-gray-100/20 hover:bg-gray-100/40 flex justify-center items-center rounded-md transition duration-300 cursor-pointer"
                  onClick={() => handleConnect()}
                >
                  <RefreshSvg
                    className={`w-5 h-5 ${
                      connectLoading ? "animate-spin" : ""
                    }`}
                  />
                </div>
              </Tooltip>
              <Tooltip title="日志(开发中)">
                <div className="w-8 h-8 bg-gray-100/20 hover:bg-gray-100/40 flex justify-center items-center rounded-md transition duration-300 cursor-pointer">
                  <AlertOutlined size={20} />
                </div>
              </Tooltip>
              <Tooltip title="主面板(开发中)">
                <div className="w-8 h-8 bg-gray-100/20 hover:bg-gray-100/40 flex justify-center items-center rounded-md transition duration-300 cursor-pointer">
                  <HomeOutlined size={20} />
                </div>
              </Tooltip>
            </div>
          </div>
          {/* 账号信息 */}
          <div className="flex gap-2 text-[12px] text-white/70 flex-wrap px-4 mt-2">
            <span>{account?.data?.username ?? "--"}</span>
            <span>{account?.data?.account_id ?? "--"}</span>
            <span>{account?.data?.uname ?? "--"}</span>
          </div>
          {/* 当前平台 */}
          <div className="flex gap-2 text-[14px] text-white/80 flex-wrap px-4 mt-2 items-center">
            <span>{platform}</span>
            {account?.success ?? true ? (
              <Tag color="processing">正常</Tag>
            ) : (
              <Tooltip title={account?.message} color="red">
                <Tag color="error">异常</Tag>
              </Tooltip>
            )}
          </div>
        </header>
        {/* 搜索 */}
        <div className="mx-6 mt-4">
          <Input
            className=" bg-gray-100/20 placeholder:text-[14px] text-white"
            placeholder="搜索功能"
            value={keyword}
            onChange={(e) => handleChangeKeyword(e.target.value)}
          />
        </div>
        <div className="flex flex-wrap mx-6 mt-2 gap-2">
          <Button size="small" onClick={() => handleChangeKeyword("广告")}>
            广告
          </Button>
          <Button size="small" onClick={() => handleChangeKeyword("创意")}>
            创意
          </Button>
          <Button size="small" onClick={() => handleChangeKeyword("热云")}>
            热云
          </Button>
          <Button size="small" onClick={() => handleChangeKeyword("创量")}>
            创量
          </Button>
        </div>
        {/* 功能列表 */}
        <div className="m-3 mt-4 flex flex-col gap-3 flex-1 overflow-y-auto overflow-x-visible">
          {_features.map((feature) => (
            <div
              className="p-3 bg-white shadow-md hover:shadow transition duration-300 rounded-xl flex justify-between items-center"
              onClick={() => handleExec(feature)}
            >
              <div>
                <div>
                  <Tag color={antdColorforPlatformMap[feature.platform]}>
                    {feature.platform}
                  </Tag>
                  <span className=" font-medium text-[14px]">
                    {feature.label}
                  </span>
                  {feature.help && (
                    <Tooltip title={feature.help}>
                      <InfoCircleOutlined className="w-3 h-3 ml-1" />
                    </Tooltip>
                  )}
                </div>
              </div>
              <Space className="">
                <Tooltip
                  title={
                    account?.data?.platform === feature.platform &&
                    !account?.success
                      ? account.message
                      : "执行"
                  }
                >
                  <Button
                    type="text"
                    className="!text-[20px]"
                    icon={<CaretRightOutlined size={30} />}
                    disabled={
                      account?.data?.platform === feature.platform &&
                      !account?.success
                    }
                  ></Button>
                </Tooltip>
                <DropdownMenu>
                  <DropdownMenuTrigger>
                    <Button
                      type="text"
                      className="!text-[20px]"
                      icon={<EllipsisOutlined />}
                    ></Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent>
                    <DropdownMenuItem onClick={() => setPinKey(feature.key)}>
                      {feature.pin ? (
                        <>
                          <PinCancelSvg className="w-4 h-4" />
                          <span>取消置顶</span>
                        </>
                      ) : (
                        <>
                          <PinSvg className="w-4 h-4" />
                          <span>置顶</span>
                        </>
                      )}
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </Space>
            </div>
          ))}
        </div>
      </div>
      {!is_content_script && <Toaster />}
    </div>
  );
}

export default App;
