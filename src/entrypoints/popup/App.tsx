import { useState, useEffect, useRef } from "react";
import Logo from "@/assets/icon.png";
import { Button, message, Space, Tag, Tooltip } from "antd";
import {
  AlertOutlined,
  CaretRightOutlined,
  EllipsisOutlined,
  HomeOutlined,
  InfoCircleFilled,
  InfoCircleOutlined,
} from "@ant-design/icons";
import { Input } from "@/components/ui/input";
import { features } from "./features";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import PinSvg from "@/assets/svg/pin.svg?react";
import PinCancelSvg from "@/assets/svg/pin_cancel.svg?react";
import { storage } from "#imports";
import RefreshSvg from "@/assets/svg/refresh.svg?react";
import { Toaster } from "@/components/ui/sonner";
import { toast } from "sonner";
import { sendMessage } from "@/message";
import "@/assets/styles/tailwind.css";

enum PlatformEnum {
  TencentAd = "腾讯广告平台",
  Reyun = "热云",
  Jd = "京东",
  CL = "创量",
}
const antdColorforPlatformMap = {
  腾讯: "processing",
  热云: "green",
  京东: "volcano",
  创量: "cyan",
};

const PIN_FEATURE_KEY_STORAGE_KEY = "local:adq_pin_feature_key";

function App(props) {
  const [platform, setPlatform] = useState<PlatformEnum>(
    PlatformEnum.TencentAd
  );
  const [_features, setFeatures] = useState<typeof features>(features);
  const pinFeatureKeysRef = useRef<Set<string>>(new Set());
  const [keyword, setKeyword] = useState("");
  const [connectLoading, setConnectLoading] = useState(false);
  const [account, setAccount] = useState<AdqResult<Account>>();

  const { is_content_script, onHide } = props;

  useEffect(() => {
    sendMessage("adq_build_connect", {}).then((data: any) => {
      setAccount(data);
    });
    if (!is_content_script) {
      browser.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
        matchPlatform(tab.url!);
      });
      browser.tabs.query({ active: true, currentWindow: true }, (tabs) => {
        matchPlatform(tabs[0].url!);
      });
    } else {
      matchPlatform(window.location.href);
    }
  }, []);

  useEffect(() => {
    storage
      .getItem<string[]>(PIN_FEATURE_KEY_STORAGE_KEY, { fallback: [] })
      .then((pinFeatureKeys) => {
        pinFeatureKeysRef.current = new Set(pinFeatureKeys || []);
        handleUpdatePin();
      });
  }, []);

  const matchPlatform = (url: string) => {
    const urlObj = new URL(url);
    if (/ad\.qq\.com/.test(urlObj.hostname))
      setPlatform(PlatformEnum.TencentAd);
    if (/data\.insightrackr\.com/.test(urlObj.hostname))
      setPlatform(PlatformEnum.Reyun);
    if (/jd\.com/.test(urlObj.hostname)) setPlatform(PlatformEnum.Jd);
  };

  const handleUpdatePin = () => {
    const newFeatures: any = [];
    // 按照置顶顺序重新将_features排序
    pinFeatureKeysRef.current.forEach((item) => {
      const feature = features.find((_item) => _item.key === item);
      if (feature) newFeatures.push({ ...feature, pin: true });
    });
    features
      .filter((item) => !pinFeatureKeysRef.current.has(item.key))
      .forEach((item) => newFeatures.push(item));
    setFeatures(newFeatures);
    storage.setItem(PIN_FEATURE_KEY_STORAGE_KEY, [
      ...pinFeatureKeysRef.current,
    ]);
  };

  const setPinKey = async (key: string) => {
    if (pinFeatureKeysRef.current.has(key)) {
      pinFeatureKeysRef.current.delete(key);
    } else {
      pinFeatureKeysRef.current = new Set([key, ...pinFeatureKeysRef.current]);
    }
    handleUpdatePin();
  };

  const handleChangeKeyword = (value) => {
    setKeyword(value);
    setFeatures(
      features.filter(
        (feature) =>
          feature.label.includes(value) || feature.platform.includes(value)
      )
    );
  };

  const handleConnect = async () => {
    setConnectLoading(true);
    await sendMessage("adq_build_connect", { force: true }).then(
      (data: any) => {
        setAccount(data);
      }
    );
    toast("刷新完成");
    setConnectLoading(false);
  };

  const handleExec = async (feature: (typeof features)[0]) => {
    if (!is_content_script) {
      const tab = await browser.tabs.query({
        active: true,
        currentWindow: true,
      });
      const url = new URL(tab[0].url!);
      if (feature.platform == "腾讯" && !url.hostname.includes("ad.qq.com"))
        return browser.tabs.create({ url: "https://ad.qq.com" });
      if (
        feature.platform == "热云" &&
        !url.hostname.includes("data.insightrackr.com")
      )
        return browser.tabs.create({ url: "https://data.insightrackr.com" });

      if (feature.href) return browser.tabs.create({ url: feature.href });

      if (feature.platform === account?.data?.platform && !account?.success)
        return toast.error(account?.message);

      if (feature.action) {
        const tab = await browser.tabs.query({
          active: true,
          currentWindow: true,
        });
        sendMessage(feature.action as any, null, tab[0].id);
      }
    } else {
      const url = new URL(window.location.href);
      if (feature.platform == "腾讯" && !url.hostname.includes("ad.qq.com"))
        return window.open("https://ad.qq.com");
      if (
        feature.platform == "热云" &&
        !url.hostname.includes("data.insightrackr.com")
      )
        return window.open("https://data.insightrackr.com/creative/material");

      if (feature.platform === account?.data?.platform && !account?.success)
        return toast.error(account?.message);

      if (feature.action) {
        sendMessage("forwardContent2Content", {
          action: feature.action,
          params: null,
        });
      }
      onHide?.();
    }
  };

  return (
    <div
      className={`adq-flex adq-flex-col adq-relative adq-bg-gray-100 ${
        is_content_script ? "adq-w-full adq-h-full" : "adq-w-[360px] adq-h-[600px]"
      }`}
    >
      <div className="adq-absolute adq-left-0 adq-top-0 adq-w-full adq-h-[40vh] adq-bg-black adq-rounded-b-4xl"></div>
      <div className="adq-relative adq-flex adq-flex-col adq-h-full">
        <header className="">
          <div className="adq-flex adq-justify-between adq-text-white adq-text-xl adq-px-4 adq-pt-6 adq-font-medium">
            <h1 className="adq-flex adq-gap-2 adq-items-center">
              <img src={Logo} className="adq-w-6 adq-h-6" />
              <span>ADQ广告助手</span>
              <Button
                type="link"
                 className="adq-text-[12px] adq-self-end-safe !adq-p-0 !adq-items-end"
                onClick={() =>
                  is_content_script
                    ? window.open(
                        "https://doc.weixin.qq.com/smartsheet/s3_AFEAugaLAKcCNga3HJC7lROqyCxgx?scode=AAUAFAedAAoI0AbWTlAFEAugaLAKc&tab=q979lj&viewId=vukaF8"
                      )
                    : browser.tabs.create({
                        url: "https://doc.weixin.qq.com/smartsheet/s3_AFEAugaLAKcCNga3HJC7lROqyCxgx?scode=AAUAFAedAAoI0AbWTlAFEAugaLAKc&tab=q979lj&viewId=vukaF8",
                      })
                }
              >{browser.runtime.getManifest().version}</Button>
              {/* <span className="adq-text-[12px] adq-self-end-safe">
                {browser.runtime.getManifest().version}
              </span> */}
            </h1>
            <div className="adq-flex adq-gap-1 adq-text-white">
              <Tooltip title="刷新连接">
                <div
                  className="adq-w-8 adq-h-8 adq-bg-gray-100/20 hover:adq-bg-gray-100/40 adq-flex adq-justify-center adq-items-center adq-rounded-md adq-transition adq-duration-300 adq-cursor-pointer"
                  onClick={() => handleConnect()}
                >
                  <RefreshSvg
                    className={`adq-w-5 adq-h-5 ${
                      connectLoading ? "adq-animate-spin" : ""
                    }`}
                  />
                </div>
              </Tooltip>
              <Tooltip title="日志(开发中)">
                <div className="adq-w-8 adq-h-8 adq-bg-gray-100/20 hover:adq-bg-gray-100/40 adq-flex adq-justify-center adq-items-center adq-rounded-md adq-transition adq-duration-300 adq-cursor-pointer">
                  <AlertOutlined size={20} />
                </div>
              </Tooltip>
              <Tooltip title="主面板(开发中)">
                <div className="adq-w-8 adq-h-8 adq-bg-gray-100/20 hover:adq-bg-gray-100/40 adq-flex adq-justify-center adq-items-center adq-rounded-md adq-transition adq-duration-300 adq-cursor-pointer">
                  <HomeOutlined size={20} />
                </div>
              </Tooltip>
            </div>
          </div>
          {/* 账号信息 */}
          <div className="adq-flex adq-gap-2 adq-text-[12px] adq-text-white/70 adq-flex-wrap adq-px-4 adq-mt-2">
            <span>{account?.data?.username ?? "--"}</span>
            <span>{account?.data?.account_id ?? "--"}</span>
            <span>{account?.data?.uname ?? "--"}</span>
          </div>
          {/* 当前平台 */}
          <div className="adq-flex adq-gap-2 adq-text-[14px] adq-text-white/80 adq-flex-wrap adq-px-4 adq-mt-2 adq-items-center">
            <span>{platform}</span>
            {account?.success ?? true ? (
              <Tag color="processing">正常</Tag>
            ) : (
              <Tooltip title={account?.message} color="red">
                <Tag color="error">异常</Tag>
              </Tooltip>
            )}
          </div>
        </header>
        {/* 搜索 */}
        <div className="adq-mx-6 adq-mt-4">
          <Input
            className=" adq-bg-gray-100/20 placeholder:adq-text-[14px] adq-text-white"
            placeholder="搜索功能"
            value={keyword}
            onChange={(e) => handleChangeKeyword(e.target.value)}
          />
        </div>
        <div className="adq-flex adq-flex-wrap adq-mx-6 adq-mt-2 adq-gap-2">
          <Button size="small" onClick={() => handleChangeKeyword("广告")}>
            广告
          </Button>
          <Button size="small" onClick={() => handleChangeKeyword("创意")}>
            创意
          </Button>
          <Button size="small" onClick={() => handleChangeKeyword("热云")}>
            热云
          </Button>
          <Button size="small" onClick={() => handleChangeKeyword("创量")}>
            创量
          </Button>
        </div>
        {/* 功能列表 */}
        <div className="adq-m-3 adq-mt-4 adq-flex adq-flex-col adq-gap-3 adq-flex-1 adq-overflow-y-auto adq-overflow-x-visible">
          {_features.map((feature) => (
            <div
              className="adq-p-3 adq-bg-white adq-shadow-md hover:adq-shadow adq-transition adq-duration-300 adq-rounded-xl adq-flex adq-justify-between adq-items-center"
              onClick={() => handleExec(feature)}
            >
              <div>
                <div>
                  <Tag color={antdColorforPlatformMap[feature.platform]}>
                    {feature.platform}
                  </Tag>
                  <span className=" adq-font-medium adq-text-[14px]">
                    {feature.label}
                  </span>
                  {feature.help && (
                    <Tooltip title={feature.help}>
                      <InfoCircleOutlined className="adq-w-3 adq-h-3 adq-ml-1" />
                    </Tooltip>
                  )}
                </div>
              </div>
              <Space className="">
                <Tooltip
                  title={
                    account?.data?.platform === feature.platform &&
                    !account?.success
                      ? account.message
                      : "执行"
                  }
                >
                  <Button
                    type="text"
                    className="!adq-text-[20px]"
                    icon={<CaretRightOutlined size={30} />}
                    disabled={
                      account?.data?.platform === feature.platform &&
                      !account?.success
                    }
                  ></Button>
                </Tooltip>
                <DropdownMenu>
                  <DropdownMenuTrigger>
                    <Button
                      type="text"
                      className="!adq-text-[20px]"
                      icon={<EllipsisOutlined />}
                    ></Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent>
                    <DropdownMenuItem onClick={() => setPinKey(feature.key)}>
                      {feature.pin ? (
                        <>
                          <PinCancelSvg className="adq-w-4 adq-h-4" />
                          <span>取消置顶</span>
                        </>
                      ) : (
                        <>
                          <PinSvg className="adq-w-4 adq-h-4" />
                          <span>置顶</span>
                        </>
                      )}
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </Space>
            </div>
          ))}
        </div>
      </div>
      {!is_content_script && <Toaster />}
    </div>
  );
}

export default App;
