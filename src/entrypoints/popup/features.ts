interface Feature {
  label: string;
  key: string;
  platform: string;
  tags: string[];
  help?: string;
  pin?: boolean;
  action?: string;
  href?: string;
}

export const features: Feature[] = [
  {
    label: "批量复制广告",
    key: "tencent-batch-copy-ad",
    action: "adq_tencent_show_copy_ad_multiple",
    platform: "腾讯",
    tags: ["广告", "复制"],
  },
  {
    label: "修改广告字段",
    key: "tencent-editor-ad-field",
    action: 'adq_tencent_open_edit_ad_field_modal',
    platform: "腾讯",
    tags: ["广告"],
  },
  {
    label: "从广告迁移创意",
    key: "tencent-copy-dc-from-ad",
    action: 'adq_tencent_open_migrate_dc_modal',
    platform: "腾讯",
    tags: ["广告", "创意"],
  },
  {
    label: "修改创意字段",
    key: "tencent-editor-dc-field",
    action: 'adq_tencent_open_update_dc_field_modal',
    platform: "腾讯",
    tags: ["创意"],
  },
  {
    label: "模板修改创意",
    key: "tencent-editor-dc-from-template",
    action: "adq_tencent_start_batch_update_creative",
    platform: "腾讯",
    tags: ["创意"],
    help: "选择多个创意, 以第一个创意为模板, 批量修改其他创意",
  },
  {
    label: "获取京东商品分类",
    key: "jd-get-category",
    action: 'adq_jd_open_category_modal',
    platform: "京东",
    tags: [],
  },
  {
    label: "素材批量下载",
    key: "reyun-batch-download-material",
    action: "adq_reyun_show_download_material_modal",
    platform: "热云",
    tags: ["广告"],
  },
  {
    label: "创量批量上传表格",
    key: "cl_batch_upload_table",
    action: "adq_reyun_show_download_material_modal",
    href: 'https://toolkit.kooler.icu/works/cl_putting',
    platform: "创量",
    tags: ["创量"],
  },
];
