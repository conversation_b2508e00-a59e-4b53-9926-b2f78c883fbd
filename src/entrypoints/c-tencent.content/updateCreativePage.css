/* .spaui-dialog-portal:has([data-hottag="Click.Function.Phoenix.Creative.Preview.Creative"]) {
  display: none !important;
} */

[data-hottag="Click.Function.report.adcreative.tab.change"]:not(:first-child) {
  display: none !important;
}
#headerAreaContentId {
  display: none !important;
}
.ant-drawer {
  z-index: 2000 !important;
}
.plugin_qq_ad_copy-loading1 {
  position: absolute;
  z-index: 2001;
}