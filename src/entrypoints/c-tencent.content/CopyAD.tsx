import { ContentScriptContext } from "#imports";
import { Checkbox, Form, InputNumber, Popconfirm } from "antd";
import Tooltip, { TooltipRef } from "antd/es/tooltip";
import { createRoot } from "react-dom/client";
import React from "react";
import { multipleAddAdGroup } from "./handles/multipleAddAdGroup";
import { useStore } from "@/store";

export const copyAdMount = async (ctx: ContentScriptContext) => {
  return createIntegratedUi(ctx, {
    position: "inline",
    anchor: ".a-table-action",
    onMount: (container: Element) => {
      document.querySelectorAll(".a-table-action").forEach((element) => {
        const span = document.createElement("a");
        span.role = "button";
        createRoot(span).render(<CopyItem />);
        element.appendChild(span);
      });
    },
  });
};

const CopyItem = () => {
  const [visible, setVisible] = useState(false);
  const $ref = useRef<TooltipRef>(null);
  const [open, setOpen] = useState(false);
  const [form] = Form.useForm();
  // const account = useAccount();
  const account = useStore((store) => store.account);

  useEffect(() => {
    // 检查特定 DOM 元素以决定组件是否可见
    if (
      document.querySelector(
        "[data-hottag='Click.Function.admanage.adgroup.create']"
      )
    )
      setVisible(true);
    else {
      console.error(
        "复制按钮: 未找到[data-hottag='Click.Function.admanage.adgroup.create']"
      );
    }
  }, []);

  useEffect(() => {
    // 监听键盘事件以在按下 Enter 时确认处理
    const func = (e: any) => {
      if (e.key === "Enter" && open) okHandle();
    };
    document.addEventListener("keyup", func);
    return () => document.removeEventListener("keyup", func);
  }, [open]);

  // 确认处理函数
  const okHandle = async (e: any = null) => {
    e?.preventDefault();
    e?.stopPropagation();
    if (!$ref.current) return;
    const { changeLocation, changeAge, copyCount, adDefaultOpen } =
      form.getFieldsValue();
    const count = Math.floor(copyCount);
    const ele =
      $ref.current.nativeElement?.parentElement?.parentElement?.querySelector(
        `[data-operation="adgroup-update"]`
      );
    const urlObj = new URL((ele as HTMLAnchorElement).href);
    const query = new URLSearchParams(urlObj.search);
    const adgroup_id = query.get("adgroup_id");
    const arr = Array.from({ length: count }, () => ({
      adgroup_id,
      changeLocation,
      changeAge,
      adDefaultOpen,
    }));

    multipleAddAdGroup(arr as any);
  };

  if (!visible) return null;
  if (!account?.success)
    return (
      <Tooltip title={account?.message}>
        <a
          className=" !text-gray-400 cursor-not-allowed"
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
          }}
        >
          复制
        </a>
      </Tooltip>
    );

  return (
    <Popconfirm
      ref={$ref}
      title="确认复制份数"
      okText="复制(Enter)"
      cancelText="取消"
      onOpenChange={(open) => {
        open && form.resetFields();
        setOpen(open);
      }}
      onConfirm={okHandle}
      description={
        <Form form={form}>
          <div style={{ margin: "10px" }}>
            <Form.Item
              label="复制份数"
              name={"copyCount"}
              noStyle
              initialValue={1}
            >
              <InputNumber
                controls={false}
                addonBefore="复制"
                addonAfter="份"
              />
            </Form.Item>
          </div>
          <div style={{ margin: "10px" }}>
            <Form.Item
              label="更改定向区域"
              name={"changeLocation"}
              valuePropName="checked"
              noStyle
              initialValue={true}
            >
              <Checkbox>更改定向区域</Checkbox>
            </Form.Item>
          </div>
          <div style={{ margin: "10px" }}>
            <Form.Item
              label="更改定向年龄"
              name={"changeAge"}
              valuePropName="checked"
              noStyle
              initialValue={true}
            >
              <Checkbox>更改定向年龄</Checkbox>
            </Form.Item>
          </div>
          <div style={{ margin: "10px" }}>
            <Form.Item
              label="广告默认为开启状态"
              name={"adDefaultOpen"}
              valuePropName="checked"
              noStyle
              initialValue={false}
            >
              <Checkbox>广告默认为开启状态</Checkbox>
            </Form.Item>
          </div>
        </Form>
      }
    >
      <a
        role="button"
        onClick={(e) => {
          e.preventDefault();
          e.stopPropagation();
        }}
      >
        复制
      </a>
    </Popconfirm>
  );
};
