import { Button, Form, Input, message, Modal, Select } from "antd";
import React, { useEffect, useRef, useState } from "react";

import "./EditDCFieldsModal.css";

import { getCreativeDetail, updateCreative } from "./handles/creative";
import { createSource } from "@/utils/source";
import { getSelectedCreativeIdsFromListPage } from "@/utils/getInfoFromPage";
import { createRoot } from "react-dom/client";
import { onMessage } from "@/message";
import { LogData } from "@/components/LogView";

export const editDCFieldMount = async (ctx) => {
  return createIntegratedUi(ctx, {
    position: "overlay",
    anchor: "body",
    onMount: (container: Element) => {
      const div = document.createElement("div");
      createRoot(div).render(<EditDCFields />);
      container.appendChild(div);
    },
  });
};

const EditDCFields = () => {
  const [fields, setFields] = useState([{ key: 1, field: null, value: "" }]);
  const [creativeIdList, setCreativeIdList] = useState<any[]>([]);
  const $container = useRef<any>(null);
  const [messageApi, contextHolder] = message.useMessage();
  const [canSubmit, setCanSubmit] = useState(false);

  const [open, setOpen] = useState(false);

  useEffect(() => {
    onMessage("adq_tencent_open_update_dc_field_modal", (data: any) => {
      setOpen(true);
    });
  }, []);

  useEffect(() => {
    if (!creativeIdList?.length) return setCanSubmit(false);
    if (!fields?.length) return setCanSubmit(false);
    if (fields.find((x) => !x.field)) return setCanSubmit(false);
    setCanSubmit(true);
  }, [creativeIdList, fields]);

  const handleGetCreativeIdFromPage = () => {
    const creativeIds = getSelectedCreativeIdsFromListPage();
    if (!creativeIds?.length) {
      messageApi.error("未获取到选中创意或不在创意列表页面");
      return;
    }
    setCreativeIdList(creativeIds);
  };

  const handleAddItem = () => {
    const newKey = fields.length
      ? Math.max(...fields.map((i) => i.key)) + 1
      : 1;
    setFields([...fields, { key: newKey, field: null, value: "" }]);
  };

  const handleDeleteItem = (key: number) => {
    setFields(fields.filter((item) => item.key !== key));
  };

  const handleSave = async () => {
    const extData = {
      total: creativeIdList.length,
      current: 0,
      successCount: 0,
      failCount: 0,
      result: [],
      hideTable: true,
    };
    // const logs = new Logs({
    //   logKey: "batchUpdateCreativeFields",
    //   extData,
    //   preFn: (log, extData) => {
    //     log.msg = `[${extData.current}/${extData.total}]: ${log.msg}`;
    //     return log;
    //   },
    // });
    const logData = new LogData({
      extra: extData,
      interceptor: (log, logs, extra) => {
        log.message = `[${extra.current}/${extra.total}]: ${log.message}`;
        return log;
      },
    })
    const source = await createSource({ logData });
    logData.start();
    for (const item of creativeIdList) {
      try {
        logData.updateExt({ current: extData.current + 1 });
        let data = await getCreativeDetail(
          { dynamic_creative_id: item },
          source
        );
        data = handleFields(data, logData);
        await updateCreative(data, source);
        logData.updateExt({ successCount: extData.successCount + 1 });
      } catch (err) {
        logData.updateExt({ failCount: extData.failCount + 1 });
      }
    }
    messageApi.success("处理成功");
    logData.end();
  };

  const handleFields = (data, logData) => {
    fields.map((item: any) => {
      if (!item.field) {
        logData.info(`字段为空`);
      }
      const fields = item.field!.split(".");
      _handleField(data, fields, item.value);
      logData.success(`处理字段成功: ${item.field}: ${item.value}`);
    });
    return data;
  };
  const _handleField = (data, fieldList, value, i = 0) => {
    if (fieldList[i] === "[map]") {
      return data.map((item) => _handleField(item, fieldList, value, i + 1));
    }
    if (i === fieldList.length - 1) {
      data[fieldList[i]] = value;
      return data;
    }
    return _handleField(data[fieldList[i]], fieldList, value, i + 1);
  };

  return (
    <Modal
      title="批量修改广告字段"
      open={open}
      onCancel={() => setOpen(false)}
      onOk={() => handleSave()}
      okButtonProps={{ disabled: !canSubmit }}
      okText="确定"
      cancelText="取消"
    >
      <div className="replaceContent" ref={$container}>
        {contextHolder}
        <p style={{ margin: "12px 0" }}>
          说明: 修改创意中某个字段的值, 如原始ID/落地页链接
        </p>
        <Form layout="vertical">
          <Form.Item
            label="创意ID"
            extra={
              <div style={{ marginTop: 8 }}>
                <Button
                  size="small"
                  type="primary"
                  style={{ marginRight: 12 }}
                  onClick={handleGetCreativeIdFromPage}
                >
                  从选中列表获取创意ID
                </Button>
                <span>多个创意ID用英文逗号分隔</span>
              </div>
            }
          >
            <Input.TextArea
              className="mainTextArea"
              placeholder="请输入创意ID, 多个创意ID用英文逗号分隔"
              value={creativeIdList.join(",")}
              onChange={(e) => setCreativeIdList(e.target.value.split(","))}
            />
          </Form.Item>
          <Form.Item label="选择和填写数据">
            <div
              style={{
                display: "flex",
                alignItems: "flex-start",
                flexDirection: "column",
                gap: 8,
              }}
            >
              {fields.map((item, index) => (
                <div key={item.key} className="itemRow">
                  <Select
                    value={item.field}
                    style={{ width: 200 }}
                    placeholder="请选择字段"
                    onChange={(value) => {
                      const newItems = [...fields];
                      newItems[index].field = value;
                      setFields(newItems);
                    }}
                    options={[
                      {
                        label: <span>落地页</span>,
                        title: "落地页",
                        options: [
                          {
                            value:
                              "creative_components.main_jump_info.[map].value.page_spec.wechat_mini_program_spec.mini_program_id",
                            label: "落地页小程序原始ID",
                          },
                          {
                            value:
                              "creative_components.main_jump_info.[map].value.page_spec.wechat_mini_program_spec.mini_program_path",
                            label: "落地页小程序链接",
                          },
                        ],
                      },
                      {
                        label: <span>创意信息</span>,
                        title: "创意信息",
                        options: [
                          { value: "dynamic_creative_name", label: "创意名称" },
                        ],
                      },
                    ]}
                    getPopupContainer={() => $container.current}
                  />
                  <Input
                    className="valueInput"
                    value={item.value}
                    onChange={(e) => {
                      const newItems = [...fields];
                      newItems[index].value = e.target.value;
                      setFields(newItems);
                    }}
                    placeholder="请输入内容"
                  />
                  {index !== 0 && (
                    <Button
                      className="deleteButton"
                      onClick={() => handleDeleteItem(item.key)}
                    >
                      ×
                    </Button>
                  )}
                </div>
              ))}
              <Button onClick={handleAddItem}>添加字段</Button>
            </div>
          </Form.Item>

          {/* <Form.Item>
          <div style={{ justifyContent: "flex-start", display: "flex", gap: 8 }}>
            <Button type="primary" disabled={!canSubmit} onClick={handleSave}>
              确认
            </Button>
            <Button onClick={onCancel}>取消</Button>
          </div>
        </Form.Item> */}
        </Form>
      </div>
    </Modal>
  );
};

