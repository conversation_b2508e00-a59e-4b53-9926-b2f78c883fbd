import {
  Button,
  Checkbox,
  InputNumber,
  message,
  Modal,
  Popconfirm,
  Space,
  Table,
} from "antd";
import React, { useEffect, useState } from "react";
import { createRoot } from "react-dom/client";

import { multipleAddAdGroup } from "./handles/multipleAddAdGroup";
import { ContentScriptContext } from "#imports";
import { useStore } from "@/store";
import { onMessage } from "@/message";
import { toast, Toaster } from "sonner";

export const copyAdMuiltipleMount = async (ctx: ContentScriptContext) => {
  return createIntegratedUi(ctx, {
    position: "inline",
    anchor: "body",
    onMount: (container: Element) => {
      const div = document.createElement("div");
      createRoot(div).render(<CopyAdMultiple />);
      container.appendChild(div);
    },
  });
};

// 复制多个广告组组件
const CopyAdMultiple = () => {
  const [modalVisible, setModalVisible] = useState(false);
  const [adgroups, setAdgroups] = useState([]);

  useEffect(() => {
    onMessage("adq_tencent_show_copy_ad_multiple", () => {
      if (!hasPermission()) return toast.error("您暂时没有创建广告的权限");
      // setModalVisible(true);
      showModal();
    });
  }, []);

  // 是否有创建权限
  const hasPermission = () => {
    return document.querySelector(
      "[data-hottag='Click.Function.admanage.adgroup.create']"
    );
  };

  // 确认操作并发送请求
  const handleOk = async (adgroups) => {
    setModalVisible(false);
    console.log(adgroups);
    multipleAddAdGroup(adgroups);
  };

  // 显示模态框并获取广告组列表
  const showModal = async () => {
    // e.preventDefault();
    // e.stopPropagation();
    const tbody = document.querySelector("tbody");
    const selected = tbody!.querySelectorAll("tr:has(.spaui-checkbox-checked)");
    if (!selected.length) return toast.error("请选勾选广告");
    const adgroups = Array.from(selected).map((item, index) => {
      const $name = item.querySelector(`.data-name-val`) as HTMLAnchorElement;
      // 复制$name节点
      const $copyName = $name.cloneNode(true) as HTMLAnchorElement;
      return {
        name: $copyName,
        adgroup_id: new URLSearchParams($name.href).get("adgroupid"),
        bid_amount: item.querySelector(".col-cost_price")?.cloneNode(true),
        marketing_content: item
          .querySelector(".col-marketing_content")
          ?.cloneNode(true),
        state: item.querySelector(".col-status_text")?.cloneNode(true),
      };
    });

    setModalVisible(true);
    setAdgroups(adgroups as any);
  };
  // if (!visible || !account?.success)
  //   return <span className="plugin_qq_ad_copy-multiple"></span>;

  return (
    <div className="adq_copy_batch_ad_root">
      {modalVisible && (
        <CopyModal
          visible={modalVisible}
          onClose={() => setModalVisible(false)}
          onConfirm={(adgroups) => handleOk(adgroups)}
          adgroups={adgroups}
        />
      )}
      <Toaster position="top-right"  />
    </div>
  );
};

// 模态框属性接口
interface ModalProps {
  visible: boolean;
  onClose: () => void;
  onConfirm: (res: any[]) => void;
  adgroups: any[];
}

// 复制模态框组件
const CopyModal = (props: ModalProps) => {
  const [_visible, _setVisible] = useState(props.visible);
  const [changeCount, setChangeCount] = useState(1);
  const [_adgroups, _setAdgroups] = useState(
    props.adgroups.map((x) => ({
      ...x,
      copyCount: 1,
      changeLocation: true,
      changeAge: true,
    }))
  );
  const [popOpen, setPopOpen] = useState(false);

  useEffect(() => {
    const func = (e) => {
      if (e.key === "Enter" && popOpen) handleMultiSetCount();
    };
    document.addEventListener("keyup", func);
    return () => document.removeEventListener("keyup", func);
  }, [popOpen]);

  useEffect(() => {
    const func = (e) => {
      if (e.key === "Enter" && !popOpen) handleOk();
    };
    document.addEventListener("keyup", func);
    return () => document.removeEventListener("keyup", func);
  }, [popOpen]);

  useEffect(() => {
    if (_visible !== props.visible) _visible ? handleOk() : props.onClose();
  }, [_visible]);

  // 确认复制操作
  const handleOk = () => {
    let res = [];
    _adgroups.map((x: any) => {
      let {
        account_id,
        adgroup_id,
        copyCount,
        changeLocation,
        changeAge,
        adDefaultOpen,
      } = x;
      res = res.concat(
        // @ts-ignore
        Array(x.copyCount).fill({
          account_id,
          adgroup_id,
          copyCount,
          changeLocation,
          changeAge,
          adDefaultOpen,
        })
      );
    });
    props.onConfirm(res);
  };

  // 设置复制数量
  const handleMultiSetCount = () => {
    _setAdgroups(_adgroups.map((x) => ({ ...x, copyCount: changeCount })));
  };

  return (
    <Modal
      title="选择批量复制份数"
      open={_visible}
      onCancel={props.onClose}
      onOk={handleOk}
      okText="复制"
      cancelText="取消"
      width={800}
    >
      <Table
        style={{ maxHeight: "80vh", fontSize: 12 }}
        size="small"
        scroll={{ x: 1000, y: 500 }}
        title={() => (
          <Space>
            <span>批量操作：</span>
            <Popconfirm
              title="批量填充复制数量"
              onConfirm={() => handleMultiSetCount()}
              okText="确认填充"
              cancelText="取消"
              onOpenChange={(open) => {
                setTimeout(() => setPopOpen(open), 300);
              }}
              description={
                <InputNumber
                  controls={false}
                  addonBefore="批量复制"
                  addonAfter="份"
                  defaultValue={changeCount}
                  onChange={(v: any) => setChangeCount(v)}
                />
              }
            >
              <Button type="link">复制数量</Button>
            </Popconfirm>
            <span>
              <Checkbox
                defaultChecked={true}
                onChange={(e) =>
                  _setAdgroups(
                    _adgroups.map((x) => ({
                      ...x,
                      changeLocation: e.target.checked,
                    }))
                  )
                }
              >
                更改区域
              </Checkbox>
            </span>
            <span>
              <Checkbox
                defaultChecked={true}
                onChange={(e) =>
                  _setAdgroups(
                    _adgroups.map((x) => ({
                      ...x,
                      changeAge: e.target.checked,
                    }))
                  )
                }
              >
                更改年龄
              </Checkbox>
            </span>
            <span>
              <Checkbox
                defaultChecked={false}
                onChange={(e) =>
                  _setAdgroups(
                    _adgroups.map((x) => ({
                      ...x,
                      adDefaultOpen: e.target.checked,
                    }))
                  )
                }
              >
                广告默认为开启状态
              </Checkbox>
            </span>
          </Space>
        )}
        dataSource={_adgroups}
        rowKey={"adgroup_id"}
        pagination={false}
        columns={[
          {
            title: "复制数量",
            width: 70,
            align: "center",
            fixed: "left",
            render: (_, record, index) => (
              <InputNumber
                size="small"
                controls={false}
                value={record.copyCount}
                style={{ width: 60, textAlign: "center" }}
                onChange={(v) => {
                  const arr = [..._adgroups];
                  arr[index].copyCount = v;
                  _setAdgroups(arr);
                }}
              />
            ),
          },
          {
            title: "更改区域",
            dataIndex: "changeLocation",
            width: 70,
            align: "center",
            render: (v, record, index) => (
              <Checkbox
                checked={v}
                onChange={(e) => {
                  const arr = [..._adgroups];
                  arr[index].changeLocation = e.target.checked;
                  _setAdgroups(arr);
                }}
              />
            ),
          },
          {
            title: "更改年龄",
            dataIndex: "changeAge",
            width: 70,
            align: "center",
            render: (v, record, index) => (
              <Checkbox
                checked={v}
                onChange={(e) => {
                  const arr = [..._adgroups];
                  arr[index].changeAge = e.target.checked;
                  _setAdgroups(arr);
                }}
              />
            ),
          },
          {
            title: "广告名称",
            dataIndex: "name",
            width: 250,
            render: (v) => (
              <div
                dangerouslySetInnerHTML={{ __html: v?.outerHTML ?? "*" }}
              ></div>
            ),
          },
          {
            title: "出价",
            width: 100,
            dataIndex: "bid_amount",
            render: (v) => (
              <div
                dangerouslySetInnerHTML={{ __html: v?.outerHTML ?? "*" }}
              ></div>
            ),
          },
          {
            title: "营销内容",
            width: 200,
            dataIndex: "marketing_content",
            render: (v) => (
              <div
                dangerouslySetInnerHTML={{ __html: v?.outerHTML ?? "*" }}
              ></div>
            ),
          },
          {
            title: "状态",
            dataIndex: "state",
            width: 80,
            render: (v) => (
              <div
                dangerouslySetInnerHTML={{ __html: v?.outerHTML ?? "*" }}
              ></div>
            ),
          },
        ]}
      ></Table>
    </Modal>
  );
};
