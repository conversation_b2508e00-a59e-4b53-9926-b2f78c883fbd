export const adgroups_get_fields =
  `targeting
adgroup_id
targeting_translation
configured_status
created_time
last_modified_time
is_deleted
system_status
adgroup_name
marketing_goal
marketing_sub_goal
marketing_carrier_type
marketing_carrier_detail
marketing_target_type
marketing_target_detail
marketing_target_id
begin_date
end_date
first_day_begin_time
bid_amount
optimization_goal
time_series
automatic_site_enabled
site_set
daily_budget
scene_spec
user_action_sets
bid_strategy
deep_conversion_spec
conversion_id
deep_conversion_behavior_bid
deep_conversion_worth_rate
deep_conversion_worth_advanced_rate
deep_conversion_behavior_advanced_bid
bid_mode
auto_acquisition_enabled
auto_acquisition_budget
smart_bid_type
smart_cost_cap
auto_derived_creative_enabled
search_expand_targeting_switch
auto_derived_landing_page_switch
data_model_version
bid_scene
marketing_target_ext
deep_optimization_type
flow_optimization_enabled
marketing_target_attachment
negative_word_cnt
search_expansion_switch
marketing_asset_id
promoted_asset_type
material_package_id
marketing_asset_outer_spec
poi_list
marketing_scene
exploration_strategy
priority_site_set
ecom_pkam_switch
forward_link_assist
conversion_name
auto_acquisition_status
cost_constraint_scene
custom_cost_cap
mpa_spec
short_play_pay_type
sell_strategy_id
dynamic_ad_type
dca_spec
additional_product_spec`.split('\n');

export const adgroups_add_fields =
  `account_id
adgroup_id
adgroup_name
marketing_goal
marketing_sub_goal
marketing_carrier_type
marketing_carrier_detail
begin_date
end_date
first_day_begin_time
bid_amount
optimization_goal
time_series
automatic_site_enabled
site_set
daily_budget
targeting
scene_spec
user_action_sets
bid_strategy
deep_conversion_spec
conversion_id
deep_conversion_behavior_bid
deep_conversion_worth_rate
deep_conversion_worth_advanced_rate
deep_conversion_behavior_advanced_bid
bid_mode
auto_acquisition_enabled
auto_acquisition_budget
smart_bid_type
smart_cost_cap
auto_derived_creative_enabled
search_expand_targeting_switch
auto_derived_landing_page_switch
bid_scene
configured_status
flow_optimization_enabled
material_package_id
marketing_asset_id
marketing_asset_outer_spec
poi_list
exploration_strategy
priority_site_set
ecom_pkam_switch
forward_link_assist
rta_id
rta_target_id
mpa_spec
cost_constraint_scene
custom_cost_cap
feedback_id
short_play_pay_type
sell_strategy_id
dynamic_ad_type
dca_spec
additional_product_spec`.split('\n');

/**
 * 推广类型-通用资产
 * @link https://developers.e.qq.com/v3.0/pages/adgroups/add
 */
// 分割线

export const bidword_add_fields =
  `adgroup_id
bidword
bid_price
use_group_price
match_type
configured_status`.split('\n');

export const dynamic_creatives_add_fields =
  `account_id
adgroup_id
dynamic_creative_name
creative_template_id
delivery_mode
dynamic_creative_type
creative_components
impression_tracking_url
click_tracking_url
program_creative_info
page_track_url
auto_derived_program_creative_switch
configured_status
enable_breakthrough_siteset`.split('\n');

export const marketing_spec_item = 
`MARKETING_TARGET_TYPE_APP_ANDROID,MARKETING_TARGET_TYPE_APP_IOS,MARKETING_TARGET_TYPE_APP_QUICK_APP,MARKETING_TARGET_TYPE_WECHAT_MINI_GAME,MARKETING_TARGET_TYPE_WECHAT_CHANNELS,MARKETING_TARGET_TYPE_MINI_PROGRAM_WECHAT,MARKETING_TARGET_TYPE_WECHAT_CHANNELS_LIVE,MARKETING_TARGET_TYPE_WECHAT_CHANNELS_LIVE_RESERVATION,MARKETING_TARGET_TYPE_CONSUMER_PRODUCT,MARKETING_TARGET_TYPE_WECHAT_OFFICIAL_ACCOUNT,MARKETING_TARGET_TYPE_LOCAL_STORE,MARKETING_TARGET_TYPE_LOCAL_STORE_PACKAGE,MARKETING_TARGET_TYPE_COMMODITY_SET,MARKETING_TARGET_TYPE_WECHAT_WORK,MARKETING_TARGET_TYPE_MINI_GAME_QQ,MARKETING_TARGET_TYPE_PC_GAME,MARKETING_TARGET_TYPE_WECHAT_STORE_PRODUCT`.split(',')
