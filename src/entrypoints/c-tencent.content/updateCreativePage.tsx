import React, { useEffect } from "react";
import { createRoot } from "react-dom/client";

import { updateCreativeMultiple } from "./handles/creative";
import "./updateCreativePage.css";
import { mainToContentMessenger } from "@/messageMainWorld";

const INJECT_CLASS_SIGN = "plugin_qq_ad_update-creative-page";

export const updateCreativeMount = async (ctx) => {
  return createIntegratedUi(ctx, {
    position: "overlay",
    anchor: 'body',
    onMount: (container: Element) => {
      const div = document.createElement("div");
      div.classList.add(INJECT_CLASS_SIGN);
      createRoot(div).render(<InjectCreativeUpdateContentUI />);
      container.appendChild(div);
    },
  });
};
// /api/v3.0/dynamic_creatives/get
// /api/v3.0/integrated_list_multiaccount/get
// /api/v3.0/dynamic_creatives/update

// https://ad.qq.com/atlas/********/admanage/index?tab=adgroup&query={%22operation_status%22:[%22CALCULATE_STATUS_EXCLUDE_DEL%22],%22system_status%22:[]}

const InjectCreativeUpdateContentUI = () => {
  const query = new URLSearchParams(window.location.search);
  const selectedList = JSON.parse(query.get("adq_list") || "[]");

  useEffect(() => {
    mainToContentMessenger.onMessage("updateCreativeRequest", (message) => {
      const [_,..._selectedList] = selectedList;
      updateCreativeMultiple?.(message.data, _selectedList);
    })
  }, []);

  useEffect(() => {
    // const roots = document.querySelector('.page-container')
    const roots = document.body;
    new MutationObserver((mutationList, observer) => {
      // 页面保存按钮
      const $submitBtn = document.querySelector(
        '.l-step-submit [data-hottag="Click.CreativePage.Submit"]:not([data-adq-executed="true"])'
      );
      if ($submitBtn) {
        $submitBtn.innerHTML = `批量修改${selectedList.length}个创意`;
        $submitBtn.setAttribute("data-adq-executed", "true");
      }

      // 二次确认弹窗自动点击
      const $comfirmSubmitModal = document.querySelector(
        '.spaui-dialog-portal:has([data-hottag="Click.Function.Phoenix.Creative.Preview.Creative"]):not([data-adq-executed="true"])'
      );
      if ($comfirmSubmitModal) {
        if (
          $comfirmSubmitModal.querySelector(
            ".spaui-button.spaui-button-primary"
          )
        ) {
          $comfirmSubmitModal.setAttribute("data-adq-executed", "true");
          const button = $comfirmSubmitModal.querySelector<HTMLButtonElement>(
            ".spaui-button.spaui-button-primary"
          );
          button?.click();
        }
      }

      // 修改提交中弹窗文案
      const $updatingModal = document.querySelector(
        '.spaui-dialog-content:has([title^="创意提交中"]):not([data-adq-executed="true"])'
      );
      if ($updatingModal) {
        $updatingModal.setAttribute("data-adq-executed", "true");
        $updatingModal.querySelector(
          '[title^="创意提交中"]'
        )!.innerHTML = `批量修改创意中, 进度: 1/${selectedList.length}`;
      }

      // 修改成功弹窗文案
      const $updatedModal = document.querySelector(
        '.spaui-dialog-content:has([data-hottag="Click.Function.Phoenix.SuccessDialogContent.goToMessageNotice"]):not([data-adq-executed="true"])'
      );
      if ($updatedModal) {
        $updatedModal.setAttribute("data-adq-executed", "true");
        var p = document.createElement("p");
        p.innerHTML = `进度:1/${selectedList.length}, 其他创意查看插件日志`;
        p.classList.add("more-info");
        $updatedModal.querySelector(".simple")!.prepend(p);
      }
    }).observe(roots, {
      childList: true,
      subtree: true,
    });
  }, []);

  return (
    <div
      style={{
        position: "fixed",
        left: "50%",
        top: 0,
        transform: "translateX(-50%)",
        zIndex: 999,
        height: 60,
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        fontWeight: "bold",
      }}
      className=" flex-wrap"
    >
      【请注意操作!】 您现在正在进行{" "}
      <span style={{ color: "red" }}>[批量修改创意]</span> 操作, 保存后会将{" "}
      <span style={{ color: "red" }}>自动同步[上一步勾选的创意]</span>
    </div>
  );
};