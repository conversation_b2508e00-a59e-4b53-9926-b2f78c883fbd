import { nanoid } from "nanoid/non-secure"

import type { Source } from "@/types/source"

import { addAdGroup } from "./adgroup"
import { addCreativeByQuery } from "./creative"
import { getAdGroup } from "./adgroup"
import { sendMessage } from "@/message"
import { LogData } from "@/components/LogView"

type Result = {
  taskId: string
  adgroupName: string
  success: boolean
  creativeTotal: number
  creativeSuccess: number
  creativeFail: number
  adgroupId?: string
}
export type ExtData = {
  total: number
  current: number
  successCount: number
  failCount: number
  result: Result[]
  adgroupName: string
  creativeTotal: number
  creativeSuccess: number
  creativeFail: number
  adgroupId?: string
}

export type AdGroupList = {
  adgroup_id: number | string
  changeLocation: boolean
  changeAge: boolean
  adDefaultOpen: boolean
}

export const multipleAddAdGroup = async (
  adGroupList: AdGroupList[],
  body = {}
) => {
  const extData: ExtData = {
    total: adGroupList.length,
    current: 0,
    successCount: 0,
    failCount: 0,
    result: [],
    adgroupName: "",
    creativeTotal: 0,
    creativeSuccess: 0,
    creativeFail: 0
  }
  // const logs = new Logs({
  //   logKey: "copyAdGroup",
  //   extData: extData,
  //   preFn: (log, extData) => {
  //     log.msg = `[${extData.current}/${extData.total}]: ${log.msg}`
  //     return log
  //   }
  // })
  const logData = new LogData({
    extra: extData,
    interceptor: (log, logs, extra) => {
      log.message = `[${extra.current}/${extra.total}]: ${log.message}`
      return log
    }
  })

  const headers = await sendMessage('adq_get_tencent_headers', {}, )
  const currentUser = await sendMessage('adq_get_current_account', {}, ) as any

  const account_id = Number(currentUser?.account_id)
  const uuid = nanoid(10)

  const source: Source = {
    task: {uuid},
    headers,
    logData,
    account_id,
    current: currentUser
  }

  try {
    for (let item of adGroupList) {
      try {
        source.task.taskId = Math.floor(Math.random() * 100000)
        // logData.newTask()
        logData.start()
        logData.updateExt({
          creativeTotal: null,
          creativeSuccess: null,
          creativeFail: null
        })
        extData.current++
        logData.info(`开始复制广告组 adgroup_id:${item.adgroup_id}...`)
        const data = await getAdGroup(
          { ...body, adgroup_id: item.adgroup_id },
          source
        )
        extData.adgroupName = data.list[0].adgroup_name
        extData.adgroupId = data.list[0].adgroup_id
        if (!data?.list?.length)
          throw new Error(`adgroup_id: ${item.adgroup_id}广告组不存在`)

        let addRes = await addAdGroup(
          {
            ...body,
            ...data.list[0],
            adgroup_id: item.adgroup_id
          },
          source,
          {
            adgroup_name: data.list[0].adgroup_name,
            changeAge: item.changeAge,
            changeLocation: item.changeLocation,
            adDefaultOpen: item.adDefaultOpen
          }
        )
        logData.success(`复制广告组 adgroup_id:${item.adgroup_id} 成功`)
        logData.info(`开始复制广告组${item.adgroup_id}关联的创意...`)

        await addCreativeByQuery(
          {
            adgroup_id: item.adgroup_id as string,
            new_adgroup_id: addRes.adgroup_id
          },
          source
        )

        logData.updateExt({ successCount: extData.successCount + 1 })
        logData.success(`复制广告组${item}关联的创意成功`)
        logData.extra.result.push({
          taskId: logData.task_id,
          adgroupId: extData.adgroupId,
          adgroupName: extData.adgroupName,
          success: true,
          creativeTotal: extData.creativeTotal,
          creativeSuccess: extData.creativeSuccess,
          creativeFail: extData.creativeFail
        })
      } catch (err:any) {
        let errStr = ""
        try {
          if (typeof err == "object") {
            errStr = JSON.stringify(err)
          } else {
            errStr = err
          }
        } catch {
          errStr = err
        }
        logData.updateExt({ failCount: extData.failCount + 1 })
        logData.error(`复制广告组 adgroup_id:${item.adgroup_id} 失败: `)
        console.error(err)
        logData.extra.result.push({
          taskId: logData.task_id,
          adgroupName: extData.adgroupName,
          adgroupId: extData.adgroupId,
          success: false,
          creativeTotal: extData.creativeTotal,
          creativeSuccess: extData.creativeSuccess,
          creativeFail: extData.creativeFail
        })
      }
    }

    logData.end()
    logData.success(`任务全部执行结束`)
  } catch (error) {
    logData.error("任务中断，请查看日志")
  }
}
