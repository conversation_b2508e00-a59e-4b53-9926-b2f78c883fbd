import dayjs from "dayjs"
import { pick } from "lodash"
import { nanoid } from "nanoid/non-secure"

import type { Source } from "@/types/source"

import { dynamic_creatives_add_fields } from "../config/fields"
import { sendMessage } from "@/message"
import { LogData } from "@/components/LogView"

export const getCreativeList = async (body, source: Source) => {
  const { logData, headers, account_id } = source
  // setLoading({currentTask: `/dynamic_creatives/add 查询创意列表中...`})
  logData.info(`/dynamic_creatives/add 查询创意列表中...`)
  const { adgroup_id } = body
  return fetch(`https://ad.qq.com/api/v3.0/integrated_list_multiaccount/get`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      ...headers
    },
    body: JSON.stringify({
      // __adq__: 1,
      account_id_list: [account_id],
      page: 1,
      page_size: 1000,
      date_range: {
        start_date: dayjs().format("YYYY-MM-DD"),
        end_date: dayjs().format("YYYY-MM-DD")
      },
      time_line: "REQUEST_TIME",
      is_total: false,
      level: "DYNAMIC_CREATIVE",
      fields: [
        "dynamic_creative.dynamic_creative_id",
        "account_id",
        "dynamic_creative.dynamic_creative_name",
        "dynamic_creative.dynamic_creative_type",
        "dynamic_creative.delivery_mode",
        "dynamic_creative.is_deleted",
        "dynamic_creative.configured_status",
        "dynamic_creative.adgroup_id",
        "dynamic_creative.creative_components",
        "adgroup.marketing_carrier_detail",
        "dynamic_creative.system_status",
        "dynamic_creative.system_status_cn",
        "dynamic_creative.dynamic_creative_status_info",
        "dynamic_creative.system_status_explanation",
        "dynamic_creative.auto_derived_program_creative_switch",
        "dynamic_creative.program_creative_info",
        "dynamic_creative.creative_template_id"
      ],
      group_by: ["dynamic_creative_id", "adgroup_id"],
      filtering: [
        {
          field: "dynamic_creative.adgroup_id",
          operator: "EQUALS",
          values: [String(adgroup_id)]
        },
        {
          field: "dynamic_creative.operation_status",
          operator: "EQUALS",
          values: ["CALCULATE_STATUS_EXCLUDE_DEL"]
        }
      ]
    })
  })
    .then((res) => res.json())
    .then((res) => {
      if (res.code != 0) return Promise.reject(res.message_cn || res.message)
      // setLoading({currentTask: `查询创意列表完成， 共${res.data.list.length}条`, taskType: "success"})
      logData.success(`查询创意列表完成， 共${res.data.list.length}条`)
      // logData.updateExt({
      //   creativeTotal: res.data.list.length
      // })
      logData.extra.creativeTotal = res.data.list.length
      return res.data.list
    })
    .catch((err) => {
      // logData.updateExt({ creativeTotal: "查询失败" })
      logData.extra.creativeTotal = "查询失败"
      logData.error(`查询创意列表失败: `)
      logData.error(err)
      return Promise.reject(err)
    })
}

export const addCreative = async (body, source: Source) => {
  const { account_id, logData, headers } = source
  // setLoading({currentTask: `/dynamic_creatives/add 复制创意中...`})
  logData.info(`/dynamic_creatives/add 开始复制创意...`)
  const { adgroup_id, data, taskId, old_adgroup_id, old_creative_id } = body
  const _data = {
    account_id: account_id,
    ...pick(data.dynamic_creative, dynamic_creatives_add_fields),
    adgroup_id: adgroup_id,
    ...removeProps(data.dynamic_creative)
  }

  // const creative_components = {...data.dynamic_creative.creative_components}
  // creative_components.brand?.map(item => {
  //   if(!item.value.brand_image_id) delete item.value.brand_image_id // 这条为空时无法添加
  // })
  // creative_components.wechat_channels?.map(item => {
  //   if(item.value.live_promoted_type === "UNKNOWN") delete item.value.live_promoted_type
  // })

  {
    const floating_zone =
      data.dynamic_creative.creative_components?.floating_zone
    if (floating_zone instanceof Array) {
      for (const { value } of floating_zone) {
        if (!value) continue
        for (const k of ["floating_zone_bgcolor"]) {
          if (value[k] === "") delete value[k]
        }
      }
    }
  }
  {
    const brand = data.dynamic_creative.creative_components?.brand
    if (brand instanceof Array) {
      for (const { value } of brand) {
        if (!value) continue
        for (const k of ["brand_image_id"]) {
          if (value[k] === "") delete value[k]
        }

        const wechat_channels_profile_spec =
          value?.jump_info?.page_spec?.wechat_channels_profile_spec
        // 视频号
        // if (wechat_channels_profile_spec && default_wechat_channels_account_id) {
        //   wechat_channels_profile_spec.username = default_wechat_channels_account_id;
        // }
      }
    }
  }
  {
    const wechat_channels =
      data.dynamic_creative.creative_components?.wechat_channels
    if (wechat_channels instanceof Array) {
      for (const { value } of wechat_channels) {
        if (!value) continue
        const k = "live_promoted_type"
        if (value[k] === "UNKNOWN") delete value[k]

        // if (default_wechat_channels_account_id) {
        //   value.username = default_wechat_channels_account_id;
        // }
      }
    }
  }

  // _data.__adq__ = 1
  return fetch(`https://ad.qq.com/api/v3.0/dynamic_creatives/add`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      ...headers
    },
    body: JSON.stringify(_data)
  })
    .then((res) => res.json())
    .then((res) => {
      if (res.code != 0) return Promise.reject(res.message_cn)
      // setLoading({currentTask: `复制创意完成`, taskType: "success"})
      logData.success(`复制创意完成`)
      sendMessage('adq_send_operation_log', {
        operation_type: "pluginCopyCreatives",
        data: {
          ..._data,
          dynamic_creative_id: res.dynamic_creative_id,
        },
        meta: pick(source, ["account_id", "current", "task"])
      })
      return res.data.list
    })
    .catch((err) => {
      // setLoading({
      //   currentTask: `/dynamic_creatives/add fail`,
      //   message: err,
      //   taskType: "error"
      // })
      logData.error(`复制创意失败: `)
      logData.error(err)
      return Promise.reject(err)
    })
}

interface ICreative {
  account_id?: number
  adgroup_id?: string
  new_adgroup_id?: string
  taskId?: string | number
}

export const addCreativeByQuery = async (body, source: Source) => {
  let successCount = 0,
    failCount = 0,
    totalCount = 0
  try {
    const { adgroup_id, new_adgroup_id }: ICreative = body
    const { account_id, task, logData, headers } = source
    const creativeList = await getCreativeList({ adgroup_id }, source)
    // if(!creativeList?.length) return Promise.reject("没有查询到创意")
    const pArr = []
    totalCount = creativeList.length
    let index = -1
    for (let item of creativeList) {
      index++
      // setLoading({currentTask: `正在复制第${index+1}条创意, 共${totalCount}条...`})
      // logData.updateExt({ currentCreative: index })
      logData.extra.currentCreative = index
      logData.info(`正在复制第${index + 1}条创意, 共${totalCount}条...`)
      await addCreative(
        {
          adgroup_id: new_adgroup_id,
          data: item,
          old_adgroup_id: adgroup_id,
          old_creative_id: item?.dynamic_creative?.dynamic_creative_id
        },
        source
      )
        .then((res) => {
          successCount = successCount + 1
          // setLoading({currentTask: `第${index+1}条创意复制成功, 共${successCount}/${totalCount}条...`, taskType: "success"})
          logData.success(
            `第${index + 1}条创意复制成功, 共${successCount}/${totalCount}条...`
          )
        })
        .catch((err) => {
          failCount = failCount + 1
          // setLoading({currentTask: `第${index+1}条创意复制失败, 共${successCount}/${totalCount}条...`, taskType: "error"})
          logData.error(
            `第${index + 1}条创意复制失败, 共${successCount}/${totalCount}条...`
          )
          logData.error(err)
        })
    }
    // setLoading({currentTask: `复制完成, 共${successCount}/${totalCount}条成功, ${failCount}条失败`, taskType: successCount ? "success": "error"})
    // logData.updateExt({
    //   creativeSuccess: successCount,
    //   creativeFail: failCount,
    //   creativeTotal: totalCount
    // })
    logData.extra.creativeSuccess = successCount
    logData.extra.creativeFail = failCount
    logData.extra.creativeTotal = totalCount
    return { successCount, failCount, totalCount }
  } catch {}
}

function removeProps({ dynamic_creative_type }) {
  //自动匹配
  if (dynamic_creative_type === "DYNAMIC_CREATIVE_TYPE_PROGRAM") {
    return {
      // program_creative_info 需要指定素材ID
    }
  }
  //手动指定
  if (dynamic_creative_type === "DYNAMIC_CREATIVE_TYPE_COMMON") {
    return {
      auto_derived_program_creative_switch: undefined,
      program_creative_info: undefined
    }
  }

  return {}
}

// /api/v3.0/dynamic_creatives/get
// /api/v3.0/integrated_list_multiaccount/get
// /api/v3.0/dynamic_creatives/update

// https://ad.qq.com/atlas/********/admanage/index?tab=adgroup&query={%22operation_status%22:[%22CALCULATE_STATUS_EXCLUDE_DEL%22],%22system_status%22:[]}

// 批量更新创意内容, 第一次网页正常操作调用接口, 后续为插件模拟调用接口
export const updateCreativeMultiple = async (body, selectedList) => {
  // const logData = new Logs({
  //   logKey: "updateCreativeMultiple",
  //   extData: {
  //     total: selectedList.length + 1,
  //     current: 1,
  //     successCount: 1,
  //     failCount: 0,
  //     notCloseWhenFinish: true
  //   }
  // })
  const logData = new LogData({})

  const headers = await sendMessage("adq_get_tencent_headers", null)
  const currentUser = await sendMessage(
    "adq_get_current_account",
    {}
  ) as any

  const account_id = Number(currentUser?.account_id)
  const uuid = nanoid(10)

  logData.start()

  logData.success(`第1条创意已完成.`)
  logData.info(`----------------------`)

  const source: Source = {
    task: { uuid },
    headers,
    logData,
    account_id,
    current: currentUser
  }
  try {
    source.task.taskId = Math.floor(Math.random() * 100000)

    let index = 0
    for (const item of selectedList) {
      try {
        logData.info(
          `正在处理第${index + 2}条创意, 共${selectedList.length + 1}条...`
        )
        logData.info(`创意ID: ${item.dcid}`)
        const _body = await getCreativeDetail(
          { dynamic_creative_id: item.dcid },
          source
        )
        // 处理字段 start
        // 以body作为字段模板
        for (const key in _body) {
          if (!body.hasOwnProperty(key)) delete _body[key]
        }
        _body.creative_components = body.creative_components
        _body.dynamic_creative_name = body.dynamic_creative_name
        // 处理字段 end
        await updateCreative(_body, source)
        logData.success(
          `第${index + 2}条创意处理完成, 进度:${index + 2}/${selectedList.length + 1}`
        )
        logData.info(`----------------------`)
        logData.updateExt({
          current: index + 2,
          successCount: index + 2
        })
        index++
      } catch (error) {
        logData.updateExt({
          current: index + 2,
          failCount: index + 2
        })
        index++
      }
    }
    logData.success(`批量更新完成`)
    logData.end()
  } catch (err:any) {
    logData.error("批量复制失败")
    logData.error(err.message ?? err)
  }
}

// 获取创意详情
export const getCreativeDetail = async (
  { dynamic_creative_id },
  source: Source
) => {
  const { logData, account_id, headers } = source
  const filtering = encodeURIComponent(
    JSON.stringify([
      {
        field: "dynamic_creative_id",
        operator: "EQUALS",
        values: [dynamic_creative_id]
      }
    ])
  )
  const fields = encodeURIComponent(
    JSON.stringify([
      "adgroup_id",
      "auto_derived_program_creative_switch",
      "click_tracking_url",
      "configured_status",
      "created_time",
      "creative_components",
      "creative_template_id",
      "delivery_mode",
      "dynamic_creative_group_used",
      "dynamic_creative_id",
      "dynamic_creative_name",
      "dynamic_creative_type",
      "impression_tracking_url",
      "is_deleted",
      "last_modified_time",
      "page_track_url",
      "system_status",
      "program_creative_info"
    ])
  )
  logData.info(`查询创意:${dynamic_creative_id}详情中...`)
  return fetch(
    `https://ad.qq.com/api/v3.0/dynamic_creatives/get?account_id=${account_id}&filtering=${filtering}&fields=${fields}`,
    {
      method: "GET",
      headers: {
        ...headers
      }
    }
  )
    .then((res) => res.json())
    .then((res) => {
      if (res.code != 0) return Promise.reject(res.message_cn || res.message)
      logData.success(`查询创意完成， 准备更新`)
      return res.data.list?.[0] || null
    })
    .catch((err) => {
      logData.error(`查询创意:${dynamic_creative_id}失败: `)
      logData.error(err)
      return Promise.reject(err)
    })
}

export const updateCreative = async (body, source: Source) => {
  const { account_id, logData, headers } = source
  logData.info(`更新创意:${body.dynamic_creative_id}中...`)
  return fetch(`https://ad.qq.com/api/v3.0/dynamic_creatives/update`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      ...headers
    },
    body: JSON.stringify({
      account_id,
      ...body
    })
  })
    .then((res) => res.json())
    .then((res) => {
      if (res.code != 0) return Promise.reject(res.message_cn || res.message)
      logData.success(`更新创意:${body.dynamic_creative_id}完成`)
      return res
    })
    .catch((err) => {
      // logs.updateExt({ creativeTotal: "查询失败" })
      logData.error(`getQuery:${body.dynamic_creative_id}失败: `)
      logData.error(err)
      return Promise.reject(err)
    })
}
