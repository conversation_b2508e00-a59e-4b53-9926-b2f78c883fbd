import dayjs from "dayjs"
import { pick, size } from "lodash"

import type { Source } from "@/types/source"

import {
  adgroups_add_fields,
  adgroups_get_fields,
  marketing_spec_item
} from "../config/fields"
import { getBidWord } from "./getBidWord"
import { sendMessage } from "@/message"

export const getAdGroup = async (body, source: Source) => {
  const { adgroup_id } = body
  const { account_id, headers, logData } = source

  // setLoading({currentTask: 'adgroups/get ...'})
  logData.info(`adgroups/get ${adgroup_id} ...`)
  const filtering = encodeURIComponent(
    JSON.stringify([
      { field: "adgroup_id", operator: "EQUALS", values: [String(adgroup_id)] }
    ])
  )
  const fields = encodeURIComponent(JSON.stringify(adgroups_get_fields))
  return fetch(
    `/api/v3.0/adgroups/get?account_id=${account_id}&filtering=${filtering}&fields=${fields}`,
    {
      method: "GET",
      headers: headers
    }
  )
    .then((response) => response.json())
    .then((res) => {
      // setLoading({currentTask: 'adgroups/get finished'})
      logData.info(`adgroups/get ${adgroup_id} finished`)
      if (res.code == 0) {
        return res.data
      } else return Promise.reject(res.message_cn || res.message)
    })
    .catch((err) => {
      // setLoading({currentTask: 'adgroups/get failed'})
      logData.error(`adgroups/get ${adgroup_id} failed: error: ${err}`)
      console.error("[error]/api/v3.0/adgroups/get", err)
      return Promise.reject(err)
    })
}

export type AdgroupFirst = {
  adgroup_name: string
  changeLocation?: boolean
  changeAge?: boolean
  adDefaultOpen?: boolean
}

export const addAdGroup = async (
  body,
  source: Source,
  options: AdgroupFirst
) => {
  const { adgroup_id } = body
  const { account_id, current, logData, task, headers } = source
  const { adgroup_name, changeAge, changeLocation, adDefaultOpen } = options
  logData.info("正在整理字段...")
  const search_bidwords = await getBidWord(
    {
      account_id,
      adgroup_id
    },
    source
  )
  const _body = {
    ...pick(body, adgroups_add_fields),
    account_id,
    adgroup_name: `${adgroup_name}_${dayjs().format("YYYYMMDDHHmmss")}`,
    search_bidwords,
    user_action_sets: size(body.user_action_sets)
      ? body.user_action_sets
      : undefined,
    auto_acquisition_budget: body.auto_acquisition_budget || undefined,
    conversion_id: body.conversion_id || undefined,
    // marketing_asset_id: undefined, //广告创建不允许使用参数marketing_asset_id，请使用marketing_asset_outer_spec
    // marketing_asset_outer_spec: body.marketing_asset_outer_spec || undefined, //下面有赋值
    marketing_asset_id: body.marketing_asset_id || undefined,
    marketing_asset_outer_spec: undefined,
    // configured_status: "AD_STATUS_SUSPEND",
    configured_status: adDefaultOpen ? "AD_STATUS_NORMAL" : "AD_STATUS_SUSPEND",
    material_package_id: body.material_package_id || undefined,
    deep_conversion_bnced_bid: body.deep_conversion_bnced_bid || undefined,
    //下面存在场景删除
    deep_conversion_behavior_bid:
      body.deep_conversion_behavior_bid || undefined,
    deep_conversion_behavior_advanced_bid:
      body.deep_conversion_behavior_advanced_bid || undefined,
    deep_conversion_worth_rate:
      body.deep_conversion_worth_advanced_rate || undefined,
    deep_conversion_worth_advanced_rate:
      body.deep_conversion_worth_advanced_rate || undefined,
    // adgroup_name: body.adgroup_name + dayjs().format('YYYYMMDDHHmmss'),
    begin_date: dayjs(
      Math.max(Date.now(), dayjs(body.begin_date).toDate().getTime())
    ).format("YYYY-MM-DD"),
    end_date:
      body.end_date && body.end_date != "0"
        ? dayjs(
            Math.max(Date.now(), dayjs(body.end_date).toDate().getTime())
          ).format("YYYY-MM-DD")
        : ""
  }

  if (marketing_spec_item.includes(body.marketing_target_type)) {
    // _body.marketing_asset_outer_spec = body.marketing_asset_id
    _body.marketing_asset_outer_spec =
      body.marketing_asset_outer_spec || undefined
    _body.marketing_asset_id = undefined
  }

  if (_body.deep_conversion_spec?.deep_conversion_type) {
    //deep_conversion_spec中已经有值
    _body.deep_conversion_behavior_bid = undefined
    _body.deep_conversion_behavior_advanced_bid = undefined
    _body.deep_conversion_worth_rate = undefined
    _body.deep_conversion_worth_advanced_rate = undefined
  }

  // 一键起量开关默认关闭
  _body.auto_acquisition_enabled = false
  // _body.auto_acquisition_enabled = true
  //一键起量开关
  if (!_body.auto_acquisition_enabled) {
    //一键起量预算，单位为分，一键起量预算定义
    _body.auto_acquisition_budget = undefined
  }
  //自动版位,site_set不能为空数组
  if (_body.automatic_site_enabled) {
    if (_body.site_set && !_body.site_set.length) {
      _body.site_set = undefined
    }
  }

  logData.info("生成区域信息...")
  const resp = await sendMessage('adq_get_tencent_region', {
    ...pick(source, ["current", "task", "account_id"]),
    data: {
      adgroup_id,
      source_ad_data: body,
      changeLocation,
      changeAge
    }
  }) as any
  if (resp?.success) {
    console.log("生成区域信息>>>", resp)
    logData.success("生成区域信息成功")
  } else {
    logData.error("生成区域信息失败")
    logData.error(resp.error?.message)
    logData.error(`traceId: ${resp.error?.trace_id}`)
    console.log(resp)
    throw resp.error
  }

  _body.targeting = {
    ...body.targeting,
    age: resp.data?.new_adgroup_info?.targeting?.age || body.targeting.age,
    geo_location:
      resp.data?.new_adgroup_info?.targeting?.geo_location ||
      body.targeting.geo_location
  }

  // _body.__adq__ = 1

  logData.info("正在添加广告组...")
  return fetch(`/api/v3.0/adgroups/add`, {
    method: "POST",
    headers: headers,
    body: JSON.stringify(_body)
  })
    .then((response) => response.json())
    .then((res) => {
      if (res.code == 0) return res.data
      return Promise.reject(res.message_cn || res.message)
    })
    .then((res) => {
      logData.success(`添加广告组成功，new_adgroup_id: ${res.adgroup_id}`)
      logData.info("确认占用区域信息...")

      sendMessage('adq_tencent_comfirm_Region', {
        data: {
          ...pick(source, ["current", "task", "account_id"]),
          data: {
            adgroup_id: task.taskId,
            new_adgroup_id: res.adgroup_id
          }
        }
      })
      
      return res
    })
    .catch((err) => {
      logData.error(`添加广告组失败: ${err}`)

      console.error("[error] /api/v3.0/adgroups/add", err)

      return Promise.reject(err)
    })
}

export const updateAdgroup = async (body, source: Source) => {
  const { account_id, logData, headers } = source
  logData.info("正在整理字段...")

  const _body:any = {
    ...body,
    // ...pick(body, adgroups_add_fields),
    account_id,
    user_action_sets: size(body.user_action_sets)
      ? body.user_action_sets
      : undefined,
    auto_acquisition_budget: body.auto_acquisition_budget || undefined,
    // conversion_id: body.conversion_id || undefined,
    // marketing_asset_id: undefined, //广告创建不允许使用参数marketing_asset_id，请使用marketing_asset_outer_spec
    // marketing_asset_outer_spec: body.marketing_asset_outer_spec || undefined, //下面有赋值
    // marketing_asset_id: body.marketing_asset_id || undefined,
    // marketing_asset_outer_spec: undefined,
    // configured_status: "AD_STATUS_SUSPEND",
    material_package_id: body.material_package_id || undefined,
    deep_conversion_bnced_bid: body.deep_conversion_bnced_bid || undefined,
    //下面存在场景删除
    deep_conversion_behavior_bid:
      body.deep_conversion_behavior_bid || undefined,
    deep_conversion_behavior_advanced_bid:
      body.deep_conversion_behavior_advanced_bid || undefined,
    deep_conversion_worth_rate:
      body.deep_conversion_worth_advanced_rate || undefined,
    deep_conversion_worth_advanced_rate:
      body.deep_conversion_worth_advanced_rate || undefined,
    // adgroup_name: body.adgroup_name + dayjs().format('YYYYMMDDHHmmss'),
    begin_date: dayjs(
      Math.max(Date.now(), dayjs(body.begin_date).toDate().getTime())
    ).format("YYYY-MM-DD"),
    end_date:
      body.end_date && body.end_date != "0"
        ? dayjs(
            Math.max(Date.now(), dayjs(body.end_date).toDate().getTime())
          ).format("YYYY-MM-DD")
        : ""
  }

  // if (marketing_spec_item.includes(body.marketing_target_type)) {
  //   // _body.marketing_asset_outer_spec = body.marketing_asset_id
  //   _body.marketing_asset_outer_spec =
  //     body.marketing_asset_outer_spec || undefined
  //   _body.marketing_asset_id = undefined
  // }

  if (_body.deep_conversion_spec?.deep_conversion_type) {
    //deep_conversion_spec中已经有值
    _body.deep_conversion_behavior_bid = undefined
    _body.deep_conversion_behavior_advanced_bid = undefined
    _body.deep_conversion_worth_rate = undefined
    _body.deep_conversion_worth_advanced_rate = undefined
  }

  // 一键起量开关默认关闭
  // _body.auto_acquisition_enabled = false
  //一键起量开关
  // if (!_body.auto_acquisition_enabled) {
  //   //一键起量预算，单位为分，一键起量预算定义
  //   _body.auto_acquisition_budget = undefined
  // }
  //自动版位,site_set不能为空数组
  if (_body.automatic_site_enabled) {
    if (_body.site_set && !_body.site_set.length) {
      _body.site_set = undefined
    }
  }

  delete _body.conversion_id

  // _body.__adq__ = 1

  logData.info("正在更新广告组...")
  return fetch(`/api/v3.0/adgroups/update`, {
    method: "POST",
    headers: headers,
    body: JSON.stringify(_body)
  })
    .then((response) => response.json())
    .then((res) => {
      if (res.code == 0) return res.data
      return Promise.reject(res.message_cn || res.message)
    })
    .then((res) => {
      logData.success(`更新广告组成功，adgroup_id: ${res.adgroup_id}`)
      
      // chromeMessaging.sendMessage("background", {
      //   type: "send_operation_log",
      //   data: {
      //     operation_type: "pluginCopyAd",
      //     data: {
      //       ..._body,
      //       adgroup_id: res.adgroup_id,
      //       old_adgroup_id: adgroup_id
      //     },
      //     meta: pick(source, ["current", "task", "account_id"])
      //   }
      // })
      return res
    })
    .catch((err) => {
      logData.error(`更新广告组失败: ${err}`)

      console.error("[error] /api/v3.0/adgroups/update", err)

      return Promise.reject(err)
    })
}
