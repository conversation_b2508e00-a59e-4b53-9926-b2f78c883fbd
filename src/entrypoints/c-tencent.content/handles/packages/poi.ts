import { sendMessage } from "@/message"
import { useStore } from "@/store"

/**
 * 获取广告门店包列表
 * @returns
 */
export const getPoiList = async () => {
  const account = useStore.getState().account
  const headers = await sendMessage("adq_get_tencent_headers", {})
  const queryData = {
    account_id: account?.data!.account_id,
    fields: [
      "created_time",
      "last_modified_time",
      "local_store_address",
      "local_store_biz_info",
      "local_store_business_area",
      "local_store_city",
      "local_store_district",
      "local_store_location",
      "local_store_name",
      "local_store_province",
      "local_store_street",
      "poi_id",
      "local_store_biz_info",
      "owner_account_id",
      "owner_account_name",
      "wechat_ecosystem_accounts",
      "local_store_remark",
      "dianping_id",
      "recommend_words",
      "system_status",
      "local_store_category",
      "local_store_type",
      "local_store_adcode",
      "wechat_work_corp_id",
      "wechat_customer_service_link",
      "image_set"
    ],
    page: 1,
    page_size: 50,
    filtering: [],
    is_fetch_all: false
  }
  const response = await fetch(
    `https://ad.qq.com/bff/mktapi/proxy?action=local_stores/get`,
    {
      method: "POST",
      headers,
      body: JSON.stringify({
        account_id: account?.data?.account_id,
        url: "local_stores/get",
        method: "GET",
        data: JSON.stringify(queryData)
      })
    }
  )
  const result = await response.json()
  if(result?.code !== 0) throw result?.message_cn
  try {
    const data = JSON.parse(result?.data)
    if(data?.code !== 0) throw data?.message_cn
    return data.data.list
  } catch (error) {
    console.log(error)
    throw error
  }
}
