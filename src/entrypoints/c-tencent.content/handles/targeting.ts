// 定向包

import { sendMessage } from "@/message"
import { useStore } from "@/store"

export const getTargetingList = async () => {
  const account = useStore.getState().account
  const headers = await sendMessage('adq_get_tencent_headers', {})
  const response = await fetch('/api/v3.0/targetings/get', {
    method: "POST",
    headers,
    body: JSON.stringify({
      account_id: account?.data!.account_id,
      page: 1,
      page_size: 100
    })
  })
  const result = await response.json()
  return result?.data?.list
}

export const getTargetSlot = async (force=false) => {
  try {
    const sessionResult = JSON.parse(sessionStorage.getItem('adq-helper_targeting_slot')!)
    if(!force && sessionResult) return sessionResult
  } catch {}
  const account = useStore.getState().account
  const headers = await sendMessage('adq_get_tencent_headers', {})
  const response = await fetch(`/ap/targeting/get_config?g_tk=${headers['g-tk']}&owner=${account?.data!.account_id}`, {
    method: "GET",
    headers,
  })
  const result = await response.json()
  if(!result?.data?.targeting_slots) return null
  sessionStorage.setItem('adq-helper_targeting_slot', JSON.stringify(result.data?.targeting_slots))
  return result?.data?.targeting_slots
}