import {pick} from "lodash"
import type { Source } from "@/types/source"

export const getBidWord = async ({account_id, adgroup_id}, source: Source) => {
  const {headers, logData} = source
  // setLoading({currentTask: "查询关键词 bidword/get ..."})
  logData.info("查询关键词 bidword/get ...")
  const filtering = JSON.stringify([{"field":"adgroup_id","operator":"EQUALS","values":[String(adgroup_id)]}])
  return fetch(`https://ad.qq.com/api/v3.0/bidword/get?account_id=${account_id}&filtering=${encodeURIComponent(filtering)}&page=1&page_size=1000`, {
    method: 'GET',
    headers: headers,
  })
  .then(response => response.json())
  .then(res => {
    if(res.code != 0) return Promise.reject(res.message_cn)
    // if(!res.data?.list?.length) return Promise.reject('bidword/get查询为空')

    // setLoading({currentTask: "整合关键词 ..."})
    logData.info("整合关键词 ...")
    return res.data?.list.map(item => pick(item, ['bid_price', 'bidword', "match_type", "source_type"]))
  })
  .catch(err => {
    // setLoading({currentTask: `bidword/get fail`, message: err, taskType: "error"})
    logData.error(`查询关键词失败 bidword/get fail:`)
    logData.error(err)
    return Promise.reject(err)
  })
}