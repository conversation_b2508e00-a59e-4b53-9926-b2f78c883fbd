import { copyAdMount } from "./CopyAD";
import "@/assets/styles/tailwind.css"
import { copyAdMuiltipleMount } from "./CopyADMultipleModal";
import { creativeMount } from "./DCList";
import { updateCreativeMount } from "./updateCreativePage";
import { editAdFieldMount } from "./EditADFieldsModal";
import { migrateDCMount } from "./MigrateDCModal";
import { editDCFieldMount } from "./EditDCFieldsModal";
// import { JDCategoryMount } from "../c-home.content/JDCategoryModal";

export default defineContentScript({
  matches: ['*://ad.qq.com/*'],
  cssInjectionMode: "manifest",
  async main(ctx) {
    // 单个复制按钮
    if(/\/atlas\/\d+\/admanage\/index/.test(window.location.pathname) && /tab=adgroup/.test(window.location.search)){
      const copyAdUi = await copyAdMount(ctx);
      copyAdUi.autoMount({
        once: false,
      });
      const copyAdMuiltipleUi = await copyAdMuiltipleMount(ctx);
      copyAdMuiltipleUi.autoMount()
    }
    if(/\/atlas\/\d+\/admanage\/index/.test(window.location.pathname) && /tab=dynamic_creative/.test(window.location.search)){
      const creativeUi = await creativeMount(ctx);
      creativeUi.autoMount()
    }
    if(/ad.qq.com\/atlas\/.*\/addelivery\/creatives-update.*&adq_list=.*/.test(window.location.href)) {
      const updateCreative = await updateCreativeMount(ctx);
      updateCreative.mount()
    }

    const editAdFieldUI = await editAdFieldMount(ctx);
    editAdFieldUI.mount()

    const migrateDCUI = await migrateDCMount(ctx);
    migrateDCUI.mount()

    const editDCFieldUI = await editDCFieldMount(ctx);
    editDCFieldUI.mount()

    // const jdCategoryUI = await JDCategoryMount(ctx);
    // jdCategoryUI.mount()
    
  },
});
