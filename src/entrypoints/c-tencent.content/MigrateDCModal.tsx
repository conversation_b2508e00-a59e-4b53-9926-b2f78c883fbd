import { Button, Form, Input, message, Modal } from "antd"
import React, { useState } from "react"


import { addCreative, getCreativeList } from "./handles/creative"
import { getSelectedAdgroundIdsFromListPage } from "@/utils/getInfoFromPage"
import { createSource } from "@/utils/source"
import { createRoot } from "react-dom/client"
import { onMessage } from "@/message"
import { LogData } from "@/components/LogView"

export const migrateDCMount = async (ctx) => {
  return createIntegratedUi(ctx, {
    position: "overlay",
    anchor: "body",
    onMount: (container: Element) => {
      const div = document.createElement("div");
      createRoot(div).render(<MigrateCreative />);
      container.appendChild(div);
    },
  });
};

export const MigrateCreative = () => {
  const [messageApi, contextHolder] = message.useMessage()
  const [form] = Form.useForm()
  const [sourceId, setSourceId] = useState<string>()
  const [targetIds, setTargetIds] = useState<string[]>([])
  const [open, setOpen] = useState(false)

  useEffect(() => {
    onMessage("adq_tencent_open_migrate_dc_modal", (data: any) => {
      setOpen(true);
    });
  }, []);

  const handleGetSourceIds = () => {
    const ids = getSelectedAdgroundIdsFromListPage()
    if (!ids?.length) {
      return messageApi.error("未获取到选中广告或不在广告列表页面")
    }
    setSourceId(ids[0])
  }
  const handleGetTargetIds = () => {
    const ids = getSelectedAdgroundIdsFromListPage()
    if (!ids?.length) {
      return messageApi.error("未获取到选中广告或不在广告列表页面")
    }
    setTargetIds(ids)
  }

  const handleSubmit = async () => {
    if (!sourceId) return messageApi.error("请先获取源广告ID")
    if (!targetIds?.length) return messageApi.error("请先获取目标广告ID")
      
    const extData = {
      total: targetIds.length,
      current: 0,
      successCount: 0,
      failCount: 0,
      result: [],
      hideTable: true
    }

    // const logs = new Logs({
    //   logKey: "batchMigrateCreative",
    //   extData,
    //   preFn: (log, extData) => {
    //     log.msg = `[${extData.current}/${extData.total}]: ${log.msg}`
    //     return log
    //   }
    // })
    const logData = new LogData({
      extra: extData,
      interceptor: (log, logs, extra) => {
        log.message = `[${extra.current}/${extra.total}]: ${log.message}`
        return log
      },
    })

    const source = await createSource({ logData })
    logData.start()

    logData.info("开始获取源广告中的创意列表")
    const creativeList = await getCreativeList({ adgroup_id: sourceId }, source)
    logData.success(`获取成功, 共${creativeList.length}个创意`)

    for (let target of targetIds) {
      try {
        logData.updateExt({ current: extData.current + 1 })
        logData.info("-----开始迁移创意到广告: " + target + "")
        let i = 0
        for (let creative of creativeList) {
          try {
            i++
            logData.info(
              `>>> 开始复制创意, ${i}/${creativeList.length}`
            )
            await addCreative({ adgroup_id: target, data: creative }, source)
            logData.success(
              `<<< 复制创意完成, ${i}/${creativeList.length}`
            )
          } catch {}
        }
        logData.info(`------迁移创意到广告:${target}完成`)
      } catch {}
    }
    logData.success("迁移完成")
  }

  return (
    <Modal title="从广告迁移创意"
    open={open}
    onCancel={() => setOpen(false)}
    onOk={() => handleSubmit()}
    okText="确定"
    cancelText="取消">
      <div>
      {contextHolder}
      <p style={{ margin: "12px 0" }}>
        说明: 批量复制某个广告中的创意到其他广告
      </p>
      <Form form={form} layout="vertical" onFinish={handleSubmit}>
        <Form.Item
          label="源广告ID"
          extra={
            <div style={{ marginTop: 8 }}>
              <Button
                size="small"
                type="primary"
                style={{ marginRight: 12 }}
                onClick={handleGetSourceIds}>
                从选中列表获取源广告ID
              </Button>
              <span>源广告ID仅支持单个</span>
            </div>
          }>
          <Input
            value={sourceId}
            placeholder="请输入目标账户ID, 仅支持一个广告ID"
            onChange={(e) => setSourceId(e.target.value)}
          />
        </Form.Item>

        <Form.Item
          label="目标广告ID"
          extra={
            <div style={{ marginTop: 8 }}>
              <Button
                size="small"
                type="primary"
                style={{ marginRight: 12 }}
                onClick={handleGetTargetIds}>
                从选中列表获取目标广告ID
              </Button>
              <span>多个创意ID用英文逗号分隔</span>
            </div>
          }>
          <Input.TextArea
            placeholder="请输入或选取目标广告ID, 多个广告ID用英文逗号分隔"
            value={targetIds.join(",")}
            onChange={(e) => setTargetIds(e.target.value.split(","))}
          />
        </Form.Item>
      </Form>
    </div>
    </Modal>
  )
}
