.replaceContent {
  /* padding: 24px 0; */
}

.formGroup {
  margin-bottom: 24px;
}

.formGroup label {
  display: block;
  margin-bottom: 8px;
  color: #333;
  font-size: 14px;
}

.mainTextArea {
  width: 100%;
  min-height: 120px;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  resize: vertical;
  font-size: 14px;
  line-height: 1.5;
}

.mainTextArea:focus {
  outline: none;
  border-color: #1a73e8;
}

.itemsList {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 16px;
}

.itemRow {
  display: flex;
  gap: 12px;
  align-items: center;
  width: 100%;
}


.deleteButton {
  width: 32px;
  height: 32px;
  border: none;
  background: none;
  color: #999;
  cursor: pointer;
  font-size: 20px;
  padding: 0;
}

.deleteButton:hover {
  color: #f56c6c;
}

.addButton {
  width: 100%;
  padding: 8px;
  border: 1px dashed #ddd;
  background: none;
  color: #666;
  cursor: pointer;
  margin-bottom: 24px;
  border-radius: 4px;
}

.addButton:hover {
  border-color: #1a73e8;
  color: #1a73e8;
}

.actionButtons {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.cancelButton,
.saveButton {
  padding: 8px 24px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.cancelButton {
  border: 1px solid #ddd;
  background: white;
  color: #666;
}

.cancelButton:hover {
  border-color: #999;
  color: #333;
}

.saveButton {
  border: none;
  background: #1a73e8;
  color: white;
}

.saveButton:hover {
  background: #1557b0;
}