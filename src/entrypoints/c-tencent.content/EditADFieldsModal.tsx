import { Button, Form, Input, message, Modal, Select, Space } from "antd";
import { cloneDeep } from "lodash";
import { nanoid } from "nanoid";
import React, { useEffect, useRef, useState } from "react";

import { getAdGroup, updateAdgroup } from "./handles/adgroup";
import { getTargetingList, getTargetSlot } from "./handles/targeting";
import { getSelectedAdgroundIdsFromListPage } from "@/utils/getInfoFromPage";
import { createSource } from "@/utils/source";

import { DynamicForm } from "@/components/DynamicForm";
import { updateAdModel } from "@/components/DynamicForm/models/updateAd";
import { createRoot } from "react-dom/client";
import { onMessage } from "@/message";
import { LogData } from "@/components/LogView";
import { DataDiff } from "@/components/DataDiff";

export const editAdFieldMount = async (ctx) => {
  return createIntegratedUi(ctx, {
    position: "overlay",
    anchor: "body",
    onMount: (container: Element) => {
      const div = document.createElement("div");
      createRoot(div).render(<EditTarget />);
      container.appendChild(div);
    },
  });
};

export const EditTarget = () => {
  const $container = useRef<any>(null);
  const [messageApi, contextHolder] = message.useMessage();
  const [canSubmit, setCanSubmit] = useState(false);
  const [adgroupIdList, setAdgroupIdList] = useState<any[]>([]);
  const [fields, setFields] = useState<
    { key: string; field: string; value: any }[]
  >([{ key: nanoid(), field: "", value: "" }]);
  const [open, setOpen] = useState(false);

  useEffect(() => {
    onMessage("adq_tencent_open_edit_ad_field_modal", (data: any) => {
      setOpen(data);
    });
  }, []);

  const handleGetAdgroupIdFromPage = () => {
    const adgroupIds = getSelectedAdgroundIdsFromListPage();
    if (!adgroupIds?.length)
      return messageApi.error("未获取到选中广告组或不在广告组列表页面");
    setAdgroupIdList(adgroupIds);
  };

  const handleAddItem = () => {
    setFields([...fields, { key: nanoid(), field: "", value: null }]);
  };

  const handleDeleteItem = (key: string) => {
    setFields(fields.filter((item) => item.key !== key));
  };

  const handleSave = async () => {
    const extData = {
      total: adgroupIdList.length,
      current: 0,
      successCount: 0,
      failCount: 0,
      result: [],
      hideTable: true,
    };
    // const logs = new Logs({
    //   logKey: "batchUpdateAdgroupTargeting",
    //   extData,
    //   preFn: (log, extData) => {
    //     log.msg = `[${extData.current}/${extData.total}]: ${log.msg}`;
    //     return log;
    //   },
    // });
    const logData = new LogData({
      extra: extData,
      interceptor: (log, logs, extra) => {
        log.message = `[${extra.current}/${extra.total}]: ${log.message}`;
        return log;
      },
    });
    const source = await createSource({ logData });
    logData.start();

    for (const adgroupId of adgroupIdList) {
      try {
        logData.updateExt({ current: extData.current + 1 });
        // 获取广告组数据
        let originData = await getAdGroup({ adgroup_id: adgroupId }, source);
        let adgroupData = cloneDeep(originData);
        if (!adgroupData?.list?.[0]) {
          logData.error(`获取广告组${adgroupId}数据失败`);
          continue;
        }
        // 更新字段值
        adgroupData = handleFields(adgroupData?.list?.[0], logData);
        // 处理数据
        if (!adgroupData.targeting?.gender) delete adgroupData.targeting.gender;
        else if (typeof adgroupData.targeting.gender == "string") {
          adgroupData.targeting.gender = JSON.parse(
            adgroupData.targeting.gender
          );
        }

        if (!adgroupData.targeting?.age) delete adgroupData.targeting.age;
        if (
          adgroupData.targeting.geo_location?.regions &&
          !adgroupData.targeting.geo_location.regions.length
        ) {
          delete adgroupData.targeting.geo_location;
        }

        // 定向包覆盖
        if (adgroupData.targeting_id) {
          const targetings = await getTargetingList();
          adgroupData.targeting = targetings.find(
            (x) => (x.id = adgroupData.targeting_id)
          )?.targeting;
          delete adgroupData.targeting_id;
        }

        // 调用更新接口
        await updateAdgroup(adgroupData, source);
        logData.success(`更新成功`);
        logData.info('正在对比数据...')
        const nextData = await getAdGroup(
          { adgroup_id: adgroupId },
          source
        );
        logData.diff(originData, nextData)
        logData.updateExt({ successCount: extData.successCount + 1 });
      } catch (error) {
        logData.error(`更新失败: ${error}`);
        logData.updateExt({ failCount: extData.failCount + 1 });
      }
    }
    logData.end();
  };

  const handleFields = (data, logData) => {
    let _fields = cloneDeep(fields);

    _fields.map((item) => {
      if (!item.field) {
        logData.info(`字段为空`);
      }
      const fields = item.field.split(".");
      _handleField(data, fields, item.value);
      logData.success(
        `处理字段成功: ${item.field}: ${
          typeof item.value === "object"
            ? JSON.stringify(item.value)
            : item.value
        }`
      );
    });
    return data;
  };
  const _handleField = (data, fieldList, value, i = 0) => {
    if (fieldList[i] === "[map]") {
      return data.map((item) => _handleField(item, fieldList, value, i + 1));
    }
    if (i === fieldList.length - 1) {
      data[fieldList[i]] = value;
      return data;
    }
    if (data[fieldList[i]] === undefined) {
      if (fieldList[i + 1] === "[map]") {
        data[fieldList[i]] = [];
      } else {
        data[fieldList[i]] = {};
      }
    }
    return _handleField(data[fieldList[i]], fieldList, value, i + 1);
  };

  // 检查表单是否可以提交
  useEffect(() => {
    const isValid = fields.every((item) => {
      if (!item.field || item.value === undefined) return false;
      const config = updateAdModel?.[item.field];

      // range类型需要检查两个值都存在
      if (config?.type === "range") {
        if (item.field == "targeting.age") {
          return (
            item.value === null ||
            (item.value?.[0]?.min != null && item.value?.[0]?.max != null)
          );
        }
        return (
          Array.isArray(item.value) &&
          item.value.length === 2 &&
          item.value[0] !== null &&
          item.value[1] !== null
        );
      }

      return true;
    });

    setCanSubmit(isValid && adgroupIdList.length > 0);
  }, [fields, adgroupIdList]);

  // 处理字段选择变化
  const handleFieldChange = async (field, index, value = null) => {
    const newItems = [...fields];
    newItems[index] = {
      ...newItems[index],
      field: field,
      value: value,
    };
    setFields(newItems);
  };

  const renderFieldItem = (item, index) => {
    if (!updateAdModel?.[item.field]) return null;
    const { type, source, ...rest } = updateAdModel[item.field];
    return (
      <DynamicForm
        key={item.field}
        style={{ flex: 1, overflow: "hidden" }}
        type={type}
        source={source}
        {...rest}
        onChange={(value) => handleFieldChange(item.field, index, value)}
      />
    );
  };

  return (
    <Modal
      title="批量修改广告字段"
      open={open}
      onCancel={() => setOpen(false)}
      onOk={() => handleSave()}
      okButtonProps={{ disabled: !canSubmit }}
      okText="确定"
      cancelText="取消"
    >
      <div className="edit-target-container" ref={$container}>
        {contextHolder}
        <p style={{ margin: "12px 0" }}>
          说明: 修改广告中某个值, 比如区域定向/年龄等
        </p>
        <Form layout="vertical">
          <Form.Item
            label="广告ID"
            extra={
              <div style={{ marginTop: 8 }}>
                <Button
                  size="small"
                  type="primary"
                  style={{ marginRight: 12 }}
                  onClick={handleGetAdgroupIdFromPage}
                >
                  从选中列表获取广告ID
                </Button>
                <span>多个广告ID用英文逗号分隔</span>
              </div>
            }
          >
            <Input.TextArea
              className="mainTextArea"
              placeholder="请输入广告ID, 多个广告ID用英文逗号分隔"
              value={adgroupIdList.join(",")}
              onChange={(e) => setAdgroupIdList(e.target.value.split(","))}
            />
          </Form.Item>
          <Form.Item
            label="选择和填写数据"
            extra="选择定向包时会将定向覆盖为定向包内容"
          >
            <div
              style={{
                display: "flex",
                alignItems: "flex-start",
                flexDirection: "column",
                gap: 8,
              }}
            >
              {fields.map((item, index) => (
                <div
                  key={item.key}
                  style={{ display: "flex", gap: 12, width: "100%" }}
                >
                  <div className="flex flex-1 gap-3 w-full overflow-hidden">
                    <Select
                      value={item.field}
                      className="!w-[140px] !shrink-0 !grow-0"
                      placeholder="请选择字段"
                      onChange={(value) => handleFieldChange(value, index)}
                      options={Object.keys(updateAdModel).map((key) => ({
                        label: <span>{updateAdModel[key].label}</span>,
                        value: key,
                      }))}
                      getPopupContainer={() => $container.current}
                    />
                    {renderFieldItem(item, index)}
                  </div>

                  {index !== 0 && (
                    <Button
                      className="deleteButton"
                      onClick={() => handleDeleteItem(item.key)}
                    >
                      ×
                    </Button>
                  )}
                </div>
              ))}
              <Button onClick={handleAddItem}>添加字段</Button>
            </div>
          </Form.Item>

          {/* <Form.Item>
          <div
            style={{ justifyContent: "flex-start", display: "flex", gap: 8 }}>
            <Button type="primary" disabled={!canSubmit} onClick={handleSave}>
              确认
            </Button>
            <Button onClick={() => setOpen(false)}>取消</Button>
          </div>
        </Form.Item> */}
        </Form>
      </div>
    </Modal>
  );
};
