import { createRoot } from "react-dom/client";
import { Toaster } from "@/components/ui/sonner";
import { toast } from "sonner";
import { onMessage } from "@/message";
import { useStore } from "@/store";

export const creativeMount = async (ctx) => {
  return createIntegratedUi(ctx, {
    position: "overlay",
    anchor: "body",
    onMount: (container: Element) => {
      const div = document.createElement("div");
      createRoot(div).render(<Creative />);
      container.appendChild(div);
    },
  });
};

export const Creative = () => {
  const account = useStore((state) => state.account);
  useEffect(() => {
    onMessage('adq_tencent_start_batch_update_creative', () => {
      console.log('adq_tencent_start_batch_update_creative')
      handleEditMultiple()
    })
  }, []);
  const getSelectedCreativeIds = () => {
    const root = document.querySelector(".spaui-table-tbody-wraper")
    const selectedRow = Array.from(root!.querySelectorAll("tr"))
      .filter(
        (item) =>
          item.querySelector("label.spaui-checkbox-checked") &&
          item.querySelector(`[data-operation="adgroup-update"]`)
      )
      .map((item) => {
        const ele = item.querySelector(
          '.a-table-action [data-operation="adgroup-update"]'
        )
        const urlObj = new URL((ele as HTMLAnchorElement).href)
        const query = new URLSearchParams(urlObj.search)
        return {
          // 改名字, 缩减url字符开销
          id: query.get("adgroup_id"), //adgroup_id
          dcid: query.get("dynamic_creative_id") //dynamic_creative_id
        }
      })
    return selectedRow
  }

  // 批量替换素材事件
  const handleEditMultiple = async () => {
    const selectedList = getSelectedCreativeIds()
    if (selectedList.length === 0) {
      toast.error("请选择需要修改的创意")
      return
    }
    const first = selectedList[0]
    let url = `https://ad.qq.com/atlas/${account?.data?.account_id!}/addelivery/creatives-update?adgroup_id=${first.id}&dynamic_creative_id=${first.dcid}`
    // 如果只有一条数据走正常的更新
    if (selectedList.length > 1) {
      url += `&adq_list=${JSON.stringify(selectedList)}`
    }
    window.open(url, "_blank")
  }
  return (
    <div>
      <Toaster position="top-right" />
    </div>
  );
};
