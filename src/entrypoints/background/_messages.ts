// import type { BaseMessage, MessageHandler } from "@/types/message"

// class MessageCenter {
//   private handlers: Map<string, MessageHandler> = new Map()

//   constructor() {
//     this.initMessageListener()
//   }

//   // 初始化消息监听
//   private initMessageListener() {
//     browser.runtime.onMessage.addListener(
//       (message: BaseMessage, sender, sendResponse) => {
//         this.handleMessage(message, sender, sendResponse)
//         return true // 保持消息通道打开
//       }
//     )
//   }

//   /**
//    * 注册消息处理器
//    * @param type - 消息类型
//    * @param handler - 消息处理器函数
//    */
//   public registerHandler(type: string, handler: MessageHandler) {
//     // 将消息类型和对应的处理器函数存储到 handlers Map 中
//     this.handlers.set(type, handler)
//   }

//   // 处理消息
//   private async handleMessage(
//     message: BaseMessage,
//     sender: Browser.runtime.MessageSender,
//     sendResponse: (response?: any) => void
//   ) {
//     try {
//       const handler = this.handlers.get(message.type)
//       if (handler) {
//         await handler(message, sender, sendResponse)
//       } else {
//         console.warn(`No handler found for message type: ${message.type}`)
//         sendResponse({
//           error: `No handler found for message type: ${message.type}`
//         })
//       }
//     } catch (error:any) {
//       console.error("Message handling error:", error)
//       sendResponse({ error: error.message })
//     }
//   }

//   // 发送消息到指定目标
//   public async sendMessage<T = any>(
//     target: "background" | "popup" | "content" | "all" | "main-world",
//     message: BaseMessage<T>
//   ): Promise<any> {
//     try {
//       if (target === "background") {
//         return browser.runtime.sendMessage(message)
//       }

//       if (target === "popup") {
//         return browser.runtime.sendMessage(message)
//       }

//       if (target === "content" || target === "all") {
//         const tabs = await browser.tabs.query({ url: "*://ad.qq.com/*" })
//         return Promise.all(
//           tabs.map((tab) => {
//             if (tab.id) {
//               return browser.tabs.sendMessage(tab.id, message)
//             }
//           })
//         )
//       }

//       if (target === "main-world") {
//         const tabs = await browser.tabs.query({ url: "*://ad.qq.com/*" })
//         return Promise.all(
//           tabs.map((tab) => {
//             if (tab.id) {
//               return browser.scripting.executeScript({
//                 target: { tabId: tab.id },
//                 func: (message) => {
//                   window.postMessage(
//                     {
//                       ...message,
//                       source: "chrome-extension"
//                     },
//                     "*"
//                   )
//                 },
//                 args: [message]
//               })
//             }
//           })
//         )
//       }
//     } catch (err) {
//       console.log(`send message err: `, err)
//     }
//   }
// }

// // 初始化消息中心并注册处理器
// const messageCenter = new MessageCenter()

// export { messageCenter }
