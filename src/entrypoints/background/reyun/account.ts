import { getReyunCookies } from "./cookies";

export const getReyunAccount = async () => {
  
  let _userInfo: Account | null = null;
  const cookies = await getReyunCookies()
  const token = await storage.getItem('local:reyun_token') as string
  const response = await fetch(
    `https://data.insightrackr.com/cas/api/user/v2/findSelf`,
    {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        cookie: cookies,
        Authorization: token 
      },
    }
  );
  const res = await response.json();
  if (res.code != '200')
    return { success: false, message: res.message, code: res.code };
    const { user } = res.data;
  _userInfo = {
    platform: "reyun",
    account_id: user.email,
    uname: user.email,
    uid: user.email,
    username: user.name,
  };
  return { success: true, data: _userInfo };
}