interface Options {
  current: Account;
  task: any;
  data: {
    adgroup_id: string;
    new_adgroup_id: string;
  };
}

/**
 * 确认使用定向区域
 * @param data
 * @returns
 */
export const comfirmRegion = async (data: Options) => {
  const {
    current,
    task,
    data: { adgroup_id, new_adgroup_id },
  } = data;
  const resp = await fetch(
    "http://adtool-fe.hsmob.com/adtools/adqq/adgroups/create_confirm",
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Sso-uid": encodeURIComponent(current.username!),
      },
      body: JSON.stringify({
        account_id: current.account_id,
        old_adgroup_id: adgroup_id,
        new_adgroup_id: new_adgroup_id,
      }),
    }
  )
    .then((res) => res.json())
    .then((res) => {
      return res.data;
    });
  return resp;
};
