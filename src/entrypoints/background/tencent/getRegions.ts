export const getRegions = async (options) => {
  const { account_id, current, task, data } = options;
  const { adgroup_id, source_ad_data, changeLocation, changeAge } = data;

  let fields: any = [];
  if (changeLocation) fields.push("targeting.geo_location");
  if (changeAge) fields.push("targeting.age");
  try {
    const resp = await fetch(
      `http://adtool-fe.hsmob.com/adtools/adqq/adgroups/copyAdBase?fields=${fields.join(
        ","
      )}`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Sso-uid": encodeURIComponent(current?.username),
        },
        body: JSON.stringify({
          account_id,
          source_ad_data,
          source_adgroup_id: adgroup_id,
          new_adgroup_id: task.taskId,
        }),
      }
    );

    const responseData = await resp.json();
    if (responseData.errcode != 0)
      throw {
        message: responseData.errmsg,
        trace_id: resp.headers.get("x-apm-trace-id"),
      };
    return { success: true, data: responseData.data };
  } catch (error) {
    // return Promise.reject(error);
    return { success: false, data: error };
  }
};
