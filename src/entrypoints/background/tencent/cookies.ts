const DOMAIN = "ad.qq.com"

export const getTencentCookies = async (): Promise<any> => {
  try {
    const cookie = {};
  const cookies = await browser.cookies.getAll({ domain: DOMAIN })
  cookies.map((item) => {
    cookie[item.name] = item.value
  })
  return cookie
  } catch (error) {
    console.error(error)
  }
}

export const getTencentCookiesAndGtk = async () => {
  const cookies = await getTencentCookies()
  var v = cookies.gdt_protect || cookies.skey
  var t = 5381
  for (var r = 0, o = v.length; r < o; ++r) {
    t += (t << 5) + v.charAt(r).charCodeAt()
  }
  return {cookies, g_tk: t & 2147483647}
}

export const getTencentHeaders = async () => {
  const {cookies, g_tk} = await getTencentCookiesAndGtk()
  return {
    cookie: cookies,
    'g-tk': g_tk,
    'content-type': 'application/json'
  }
}

