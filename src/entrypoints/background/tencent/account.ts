import { getTencentCookiesAndGtk } from "./cookies";

export const getTencentAccountIdFromTab = (tab) => {
  try {
    if (!tab) return null;
    let url = new URL(tab.url);
    // 如果域名不是DOMAIN, 则返回null
    if (url.hostname !== "ad.qq.com") return null;
    const match = url.pathname.match(/^\/atlas\/(\d+)/);
    return match?.[1];
  } catch (error) {
    return null;
  }
};

export const getTencentAccount = async (
  options: { force?: boolean; account_id?: string } = {}
) => {
  let { account_id } = options;
  if (!account_id) {
    const tabs = await browser.tabs.query({
      active: true,
      // currentWindow: true,
    });
    account_id = getTencentAccountIdFromTab(tabs?.[0]) as string;
  }
  if (!account_id)
    return {
      success: false,
      message: "未获取到腾讯广告账户",
      code: "not_found_tencent_account_id",
    };

  const res = await getAccountInfo(account_id);
  return res;
};

export const getAccountInfo = async (
  account_id
): Promise<AdqResult<Account | null>> => {
  if (!account_id)
    return {
      success: false,
      message: "未获取到腾讯广告账户",
      code: "not_found_tencent_account_id",
    };
  const { cookies, g_tk } = await getTencentCookiesAndGtk();
  let _userInfo: Account | null = null;
  const response = await fetch(
    `https://ad.qq.com/ap/advertiser/basic?g_tk=${String(
      g_tk
    )}&owner=${account_id}`,
    {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        cookie: cookies,
        "g-tk": String(g_tk),
      },
    }
  );
  const res = await response.json();
  if (res.code != 0)
    return { success: false, message: res.msg, code: res.code };
  const { advertiser, operator } = res.data;
  _userInfo = {
    platform: "tencent",
    account_id,
    uname: advertiser.uname,
    uid: advertiser.uid,
    username: operator.operator_name,
  };
  return { success: true, data: _userInfo };
};
