
export const cancelRegion = async (req, res) => {
  const resp = await fetch(
    "http://adtool-fe.hsmob.com/adtools/adqq/adgroups/create_cancel",
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        // "Sso-uid": encodeURIComponent(userinfo.username),
      },
      body: JSON.stringify({
        account_id: req.body.account_id,
        adgroup_id: req.body.adgroup_id,
      }),
    }
  )
    .then((res) => res.json())
    .then((res) => {
      return res.data;
    })
    .catch((err) => {
      console.log(err);
    });

  res.send({
    data: resp,
  });
};
