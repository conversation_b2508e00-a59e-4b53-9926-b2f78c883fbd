// import type { <PERSON><PERSON><PERSON><PERSON> } from "@/types/message";
// import { errStack } from "./handles/errStack";
// import { init } from "./handles/init";
// import { handleSendOperationLogs } from "./handles/operationLog";
// import { store } from "@/store/background";
// import { getCookiesAndGtk } from "./handles/cookies";
// import { getRegions } from "./handles/getRegions";
// import { getCurrentTab, setActiveTab } from "./handles/tab";
// import { getPluginConfig } from "./handles/pluginConfig";

// export const Handlers: Record<string, MessageHandler> = {
//   get_store: (message, sender, sendResponse) => {
//     sendResponse({ success: true, data: store.state });
//   },
//   retry_connect: async (message, sender, sendResponse) => {
//     try {
//       await init();
//       sendResponse({ success: true });
//     } catch (error: any) {
//       errStack.push(error?.message ?? error);
//       sendResponse({ success: false, error });
//     }
//   },
//   get_current_tab: async (message, sender, sendResponse) => {
//     const tab = await getCurrentTab();
//     sendResponse(tab.id);
//   },
//   set_active_tab: async (message, sender, sendResponse) => {
//     const { tabId } = message.data;
//     await setActiveTab(tabId);
//     sendResponse({ success: true });
//   },
//   send_operation_log: async (message) => {
//     const { data, meta, operation_type } = message.data;
//     handleSendOperationLogs(operation_type, data, meta);
//   },
//   get_cookies_and_gtk: async (message, sender, sendResponse) => {
//     const { cookies, g_tk } = await getCookiesAndGtk();
//     sendResponse({ success: true, data: { cookies, g_tk } });
//   },
//   get_regions: async (message, sender, sendResponse) => {
//     try {
//       const resp = await getRegions(message);
//       sendResponse({ success: true, data: resp });
//     } catch (error: any) {
//       errStack.push(error?.message ?? error);
//       sendResponse({ success: false, error });
//     }
//   },

//   get_plugin_config: async (message, sender, sendResponse) => {
//     try {
//       const data = await getPluginConfig();
//       sendResponse({ success: true, data });
//     } catch (error: any) {
//       errStack.push(error?.message ?? error);
//       sendResponse({ success: false, error });
//     }
//   },
// };
