import type { MessageToBackgroundBody } from "@/types/source";

const host = import.meta.env.PLASMO_PUBLIC_ADXHS_API_HOST;

export const getHeaders = (meta: MessageToBackgroundBody) => ({
  "Content-Type": "application/json",
  "account-id": String(meta.current?.account_id),
  "x-username": encodeURIComponent(meta.current!.username!),
  "Sso-Uid": meta.current!.user_id!,
  "x-uname": encodeURIComponent(meta.current!.uname!), // 主体名称
});

type OperationType =
  | "pluginCopyAd"
  | "pluginCopyCreatives"
  | "addAdGroup"
  | "deleteAdGroup"
  | "updateAdGroup"
  | "batchUpdateAdGroup"
  | "configuredStatusAdGroup"
  | "addCreative"
  | "deleteCreative";

interface Options {
  operation_type: OperationType;
  data: any;
  meta: MessageToBackgroundBody;
}
export const handleSendOperationLogs = async (options: Options) => {
  const { operation_type: type, data, meta } = options;
  try {
    await fetch(`${host}/ad-tencent-plugin/log/v2/send-operation-logs`, {
      method: "POST",
      headers: getHeaders(meta),
      body: JSON.stringify({
        type,
        data,
        meta,
      }),
    });
  } catch (error) {
    console.error(error);
  }
};
