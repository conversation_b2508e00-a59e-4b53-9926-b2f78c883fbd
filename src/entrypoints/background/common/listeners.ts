import { connect, getPlatform } from "./connect";
import { getCurrentAccount, setCurrentAccount } from "./account";
import { handleSendOperationLogs } from "./operation";
import { onMessage, sendMessage } from "@/message";

// 监听连接更改
let connectPromise: any = null;
onMessage("adq_build_connect", ({ data }: any) => {
  if (!connectPromise) {
    connectPromise = connect({ force: !!data?.force });
    setTimeout(() => {
      connectPromise = null;
    }, 1000);
  }
  return connectPromise;
});

browser.tabs.onUpdated.addListener(async (tabId, changeInfo) => {
  if (changeInfo.url) {
    connectPromise = null;
    const url = changeInfo.url;
    const platform = getPlatform(url);
    if (platform) {
      const res = await connect({ force: true, platform });
      if (!res?.success || res.data?.canUseMsg) {
        sendMessage("adq_connect_fail", res as any, tabId);
        setCurrentAccount(res!.data as Account);
      }
    }
  }
});

onMessage("forwardContent2Content", async ({data}:any) => {
  const tabs = await browser.tabs.query({ active: true });
  sendMessage(data.action, data.params, tabs[0]!.id)
})

onMessage("adq_get_current_account", getCurrentAccount);

onMessage("adq_update_log_sender", ({data, sender}:any) => {
  console.log('adq_update_log_sender', data, sender)
  sendMessage('adq_update_log_port', data, sender.tab.id)
});
 
onMessage("adq_send_operation_log", ({ data }: any) =>
  handleSendOperationLogs(data)
);

onMessage("adq_get_tab_id", () =>
  browser.tabs
    .query({ active: true, currentWindow: true })
    .then((tabs) => tabs[0]?.id)
    .catch(() => null)
);

onMessage("adq_set_active_tab", async ({data}:any) => {
  const res = await browser.tabs.update(data.tabId, {active: true})
  return res
})
