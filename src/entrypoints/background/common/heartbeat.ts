/**
 * Tracks when a service worker was last alive and extends the service worker
 * lifetime by writing the current time to extension storage every 20 seconds.
 * You should still prepare for unexpected termination - for example, if the
 * extension process crashes or your extension is manually stopped at
 * chrome://serviceworker-internals. 
 */
/**
 * 通过每20秒向扩展存储写入当前时间来跟踪 service worker 的存活状态并延长其生命周期。
 * 你仍然需要为意外终止做好准备 - 例如，当扩展进程崩溃或在 chrome://serviceworker-internals
 * 页面手动停止扩展时。
 */
let heartbeatInterval;

/**
 * 执行心跳检测
 * 将当前时间戳写入到扩展的本地存储中
 */
async function runHeartbeat() {
  await browser.storage.local.set({ 'last-heartbeat': new Date().getTime() });
  console.log('Heartbeat ...');
}

/**
 * 启动心跳检测间隔，保持 service worker 活跃。
 * 当你需要确保持久性工作时谨慎调用此函数，
 * 完成工作后记得调用 stopHeartbeat。
 */
export async function startHeartbeat() {
  // 在 service worker 启动时执行一次心跳
  runHeartbeat().then(() => {
    console.log('Heartbeat started.');
    // 之后每20秒执行一次
    heartbeatInterval = setInterval(runHeartbeat, 20 * 1000);
  });
}

/**
 * 停止心跳检测
 */
export async function stopHeartbeat() {
  clearInterval(heartbeatInterval);
}

/**
 * 获取存储在扩展存储中的最后一次心跳时间
 * 如果从未执行过心跳检测，则返回 undefined
 */
export async function getLastHeartbeat() {
  return (await browser.storage.local.get('last-heartbeat'))['last-heartbeat'];
}