
export const check = async ({account_id, username, platform}) => {
  try {
    if(!account_id) throw "当前页面未检测到广告账户, 请进入投放平台广告列表页面重新打开插件"
    const response = await fetch("http://adtool-fe.hsmob.com/adtools/adqq/account/check", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Sso-Uid": encodeURIComponent(username)
      },
      body: JSON.stringify({
        type: platform,
        account_id: account_id
      })
    })
    const result = await response.json()

    const traceId = response.headers.get("X-Apm-Trace-Id")
    if(result.errcode == "*************") {
      throw `查询授权失败，请确认账户是否授权, trace_id: ${traceId}`
    }
    if(result.errcode != "0") throw `查询失败, ${result.errmsg}, trace_id: ${traceId}`
    return {canUse: true, message: ''}
      
  } catch (error:any) {
    return {canUse: false, message: error?.message ?? error ?? '当前账号无权限'}
  }
}