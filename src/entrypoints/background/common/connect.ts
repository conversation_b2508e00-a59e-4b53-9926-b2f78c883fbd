import { storage } from "#imports";
import dayjs from "dayjs";
import { getTencentAccount } from "../tencent/account";
import { check } from "./check";
import { setCurrentAccount } from "./account";
import { sendMessage } from "@/message";
import { getReyunAccount } from "../reyun/account";

const ADQ_STORAGE_COMMAN_KEY = "local:adq_common";

type ConnectInfo = Account[];

export const getPlatform = (url) => {
  let _url = new URL(url);
  if (_url.hostname.includes("ad.qq.com")) return "tencent";
  if (_url.hostname.includes("data.insightrackr.com")) return "reyun";
};

export async function connect({
  force,
  platform,
}: {
  force?: boolean;
  platform?: string;
}) {
  try {
    if (!platform) {
      const tab = await browser.tabs.query({ active: true });
      const url = tab?.[0]?.url;
      platform = getPlatform(url);
    }

    let accountRes: AdqResult<Account | null>;

    switch (platform) {
      case "tencent":
        accountRes = await getTencentAccount({ force });
        break;
      case "reyun":
      // return await connectReyun({force})
        accountRes = await getReyunAccount()
        break;
      default:
        return {
          success: false,
          message: "未检测到可用网页",
          code: "platform_not_found",
        };
    }
    
    if (!accountRes.success) return accountRes;

    const account = accountRes.data as Account;

    const _storage = await storage.getItem<ConnectInfo>(
      ADQ_STORAGE_COMMAN_KEY,
      {
        fallback: [],
      }
    );

    let index = _storage.findIndex(
      (item) => item.account_id === account.account_id
    );

    if(index === -1) index = 0

    _storage[index] = account;
    _storage[index].date = dayjs().format("YYYY-MM-DD HH:mm:ss");
    const { canUse, message } = await check({
      account_id: account.account_id,
      username: account.username,
      platform: account.platform,
    });
    _storage[index].canUse = canUse;
    _storage[index].canUseMsg = message;
    await storage.setItem(ADQ_STORAGE_COMMAN_KEY, _storage);

    let result: AdqResult<Account> = { success: true, data: account };

    if (!canUse) {
      result = { success: false, data: account, message };
    }

    const tab = await browser.tabs.query({ active: true });
    sendMessage(
      "adq_update_account",
      result as any,
      tab[0]?.id
    );

    setCurrentAccount(account);

    return result;
  } catch (error) {
    console.error(error);
    return null
  }
}
