// import { startHeartbeat } from "./common/heartbeat";

// import { Handlers } from "./handlers";
// import { messageCenter } from "./messages";
// import { init } from "./handles/init";

// export const DOMAIN = "ad.qq.com";

import "./common/connect"
import { startHeartbeat } from "./common/heartbeat";
import './listeners'

export default defineBackground(() => {
  console.log("插件ID：", browser.runtime.id);

  startHeartbeat();
  // init();

  // Object.entries(Handlers).forEach(([type, handler]) => {
  //   messageCenter.registerHandler(type, handler);
  // });

  // // 设置side panel
  // browser.sidePanel
  //   .setPanelBehavior({ openPanelOnActionClick: true })
  //   .catch((error) => console.error(error));
});
