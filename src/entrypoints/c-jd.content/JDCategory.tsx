import { createRoot } from "react-dom/client";

export const JDCategoryMount = async (ctx) => {
  return createIntegratedUi(ctx, {
    position: "overlay",
    anchor: "body",
    onMount: (container: Element) => {
      // const div = document.createElement("div");
      // createRoot(div).render(<JDCategory />);
      // container.appendChild(div);
      // 使用 MutationObserver 监听节点变化
      const MAX_WAIT_TIME = 10000; // 最大等待时间 10 秒
      let observer: MutationObserver;
      let timeoutId: NodeJS.Timeout;

      const startObserver = () => {
        // 设置超时检查
        timeoutId = setTimeout(() => {
          if (observer) {
            observer.disconnect();
            window.opener.postMessage(
              {
                type: "jd_category_result",
                data: {
                  success: false,
                  error: "获取商品分类超时",
                  url: window.location.href.split("?")[0],
                },
              },
              "*"
            );
            window.close();
          }
        }, MAX_WAIT_TIME);

        if (
          document.querySelectorAll(".crumb-wrap .crumb .item:not(.sep)").length >
          0
        ) {
          extractJDCategory();
          return;
        }

        // 创建观察器
        observer = new MutationObserver((mutations, obs) => {
          const breadcrumbNav = document.querySelectorAll(
            ".crumb-wrap .crumb .item:not(.sep)"
          );
          if (breadcrumbNav.length > 0) {
            clearTimeout(timeoutId);
            obs.disconnect();
            extractJDCategory();
          }
        });

        // 开始观察
        observer.observe(document.body, {
          childList: true,
          subtree: true,
        });
      };

      // 开始监听节点变化
      startObserver();
    },
  });
};

const extractJDCategory = () => {
  try {
    // 获取商品分类信息
    const breadcrumbNav = document.querySelectorAll(
      ".crumb-wrap .crumb .item:not(.sep)"
    );

    const category = Array.from(breadcrumbNav)
      .filter((_, index) => index < 3)
      .map((item) => item.textContent?.trim())
      .join(">");
    const name =
      document.querySelector(".sku-name-title")?.textContent?.trim() || "";

    // 发送消息给父窗口
    window.opener.postMessage(
      {
        type: "jd_category_result",
        data: {
          success: true,
          category,
          name,
          url: window.location.href.split("?")[0],
          id: window.location.href.match(/\d+/)?.[0] || "", // 提取商品ID，需要根据实际情况修改正则表达式或其他方法来提取商品ID
        },
      },
      "*"
    );
  } catch (error) {
    // 发送错误消息给父窗口
    window.opener.postMessage(
      {
        type: "jd_category_result",
        data: {
          success: false,
          error: error instanceof Error ? error.message : "未知错误",
          url: window.location.href.split("?")[0],
        },
      },
      "*"
    );
  }
  // finally {
  //   // 延迟关闭标签页，确保消息发送完成
  //   setTimeout(() => {
  //     window.close();
  //   }, 500);
  // }
};
