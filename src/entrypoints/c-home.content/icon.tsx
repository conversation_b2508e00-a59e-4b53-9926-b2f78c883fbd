import { createRoot } from "react-dom/client";
import App from "../popup/App";
// import {
//   Drawer,
//   DrawerClose,
//   DrawerContent,
//   DrawerDescription,
//   DrawerFooter,
//   DrawerHeader,
//   DrawerTitle,
//   DrawerTrigger,
// } from "@/components/ui/drawer";
import Logo from "@/assets/icon.png";
import "./main.css";
import { Drawer } from "antd";

export const iconMount = async (ctx) => {
  return createIntegratedUi(ctx, {
    // name: "adq-home",
    position: "overlay",
    anchor: "body",
    onMount: (container: Element) => {
      const div = document.createElement("div");
      div.id = "adq-home-icon";
      div.onclick = (e) => {
        e.stopPropagation();
        // e.preventDefault();
      };
      createRoot(div).render(<Home />);
      container.appendChild(div);
    },
  });
};

const Home = () => {
  const [visible, setVisible] = useState(false);
  return (
    <div>
      <div className=" adq-fixed adq-top-[100px] adq-left-4 adq-border-2 adq-border-white adq-rounded-full adq-overflow-hidden adq-w-10 adq-h-10 adq-z-[998]" onClick={() => setVisible(true)}>
        <img src={Logo} alt="logo" className="adq-w-full adq-h-full" />
      </div>
      <Drawer placement="left" open={visible} onClose={() => setVisible(false)} closeIcon={null} className="adq-z-[9999] adq_drawer_content !adq-p-0" styles={{body: {padding: 0}}} destroyOnHidden>
        <App is_content_script={true} onHide={() => setVisible(false)} />
      </Drawer>
    </div>
  );
};
