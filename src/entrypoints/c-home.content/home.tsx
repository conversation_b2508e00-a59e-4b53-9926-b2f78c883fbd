import { createRoot } from "react-dom/client";
import { Toaster } from "@/components/ui/sonner";
import { toast } from "sonner";
import { onMessage, sendMessage } from "@/message";
import { mainToContentMessenger } from "@/messageMainWorld";

export const mainMount = async (ctx) => {
  return createIntegratedUi(ctx, {
    // name: "adq-home",
    position: "overlay",
    anchor: "body",
    onMount: (container: Element) => {
      const div = document.createElement("div");
      createRoot(div).render(<Home />);
      container.appendChild(div);
    },
  });
};

export const Home = () => {
  useEffect(() => {
    // onMessage("adq_connect_fail", ({ data }: any) => {
    //   toast.error(data.message);
    // });
    mainToContentMessenger.onMessage('forwardToBackground', async (message) => {
      // 将消息转发给 background
      const result = await sendMessage(message.data!.action as any, message.data!.params);
      // 将 background 的返回值回传给主世界
      // mainToContentMessenger.sendMessage('backgroundResponse', result);
    });
  }, []);
  return (
    <div>
      <Toaster position="top-right"  />
    </div>
  );
};
