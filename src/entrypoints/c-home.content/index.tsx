import "@/store/index"
import "@/assets/styles/tailwind.css";
import { mainMount } from "./home";
import { loadingMount } from "./loading";
import { JDCategoryMount } from "./JDCategoryModal";
import { iconMount } from "./icon";



export default defineContentScript({
  matches: ["*://ad.qq.com/*", '*://data.insightrackr.com/*'],
  cssInjectionMode: "manifest",
  async main(ctx) {

    await injectScript('/inject.js', {
      keepInDom: true,
    });
    console.log('Done!');

    const homeUi = await mainMount(ctx);
    homeUi.mount();
    const _loadingMount = await loadingMount(ctx)
    _loadingMount.mount();

    const iconUi = await iconMount(ctx);
    iconUi.mount();

    const jdCategoryUI = await JDCategoryMount(ctx);
    jdCategoryUI.mount()
  },
});
