import { createRoot } from "react-dom/client";
import { Drawer } from "antd";
import { onMessage } from "@/message";
import { LogItem, LogList } from "@/components/LogView";

export const loadingMount = async (ctx) => {
  return createIntegratedUi(ctx, {
    // name: "adq-home",
    position: "overlay",
    anchor: "body",
    onMount: (container: Element) => {
      const div = document.createElement("div");
      createRoot(div).render(<Loading />);
      container.appendChild(div);
    },
  });
};

export const Loading = () => {
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [logs, setLogs] = useState<LogItem[]>();

  useEffect(() => {
    onMessage("adq_update_log_port", ({ data }: any) => {
      console.log("adq_update_log_port", data);
      setLogs(data.logs);
      if(!drawerOpen) setDrawerOpen(true);
      return true;
    });
  }, []);

  return (
    <Drawer
      title="任务运行日志"
      open={drawerOpen}
      zIndex={1002}
      width={800}
      onClose={() => {
        setDrawerOpen(false);
      }}
    >
      <div
        style={{
          height: "100%",
          overflowX: "hidden",
          overflowY: "auto",
        }}
      >
        <LogList logs={logs!} />
      </div>
    </Drawer>
  );
};
