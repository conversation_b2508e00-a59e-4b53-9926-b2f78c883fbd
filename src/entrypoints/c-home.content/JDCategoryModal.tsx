import React, { useState, useCallback, useEffect, useRef } from "react";
import { Input, Button, Table, message, Space, Modal } from "antd";
// import { chromeMessaging } from '~/utils/chromeMessaging';
import * as XLSX from "xlsx";
import { createRoot } from "react-dom/client";
import { onMessage, sendMessage } from "@/message";


interface ProductCategory {
  id: string;
  name?: string;
  category1?: string;
  category2?: string;
  category3?: string;
  url?: string;
  status?: "pending" | "success" | "error";
  message?: string;
}

export const JDCategoryMount = async (ctx) => {
  return createIntegratedUi(ctx, {
    position: "overlay",
    anchor: "body",
    onMount: (container: Element) => {
      const div = document.createElement("div");
      createRoot(div).render(<JDCategory />);
      container.appendChild(div);
    },
  });
};

export const JDCategory = () => {
  const [productIds, setProductIds] = useState<string>("");
  const [loading, setLoading] = useState(false);
  const [categories, setCategories] = useState<ProductCategory[]>([]);
  const promiseRef = useRef<PromiseWithResolvers<any>>(null);

  const [open, setOpen] = useState(false);

  useEffect(() => {
    onMessage("adq_jd_open_category_modal", (data: any) => {
      setOpen(true);
    });
  }, []);

  const columns = [
    {
      title: "商品ID",
      dataIndex: "id",
      key: "id",
    },
    {
      title: "商品名称",
      dataIndex: "name",
      key: "name",
      render: (text: string, record: ProductCategory) => (
        <a
          href={record.url}
          target="_blank"
          style={{
            display: "block",
            width: "100%",
            whiteSpace: "nowrap",
            overflow: "hidden",
            textOverflow: "ellipsis",
          }}
          title={text}
        >
          {text}
        </a>
      ),
    },
    {
      title: "一级分类",
      dataIndex: "category1",
      key: "category1",
    },
    {
      title: "二级分类",
      dataIndex: "category2",
      key: "category2",
    },
    {
      title: "三级分类",
      dataIndex: "category3",
      key: "category3",
    },
    {
      title: "状态",
      key: "status",
      render: (_: any, record: ProductCategory) => (
        <span
          style={{
            color:
              record.status === "error"
                ? "red"
                : record.status === "success"
                ? "green"
                : "orange",
          }}
        >
          {record.status === "pending"
            ? "处理中"
            : record.status === "success"
            ? "成功"
            : "失败"}
          {record.message && ` (${record.message})`}
        </span>
      ),
    },
  ];

  const handleExport = useCallback(() => {
    if (!categories.length) {
      message.warning("没有可导出的数据");
      return;
    }

    const ws = XLSX.utils.json_to_sheet(categories);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, "Categories");
    XLSX.writeFile(wb, "jd-categories.xlsx");
  }, [categories]);

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setProductIds(e.target.value);
  };

  const parseInput = (input: string) => {
    return input
      .split("\n")
      .map((line) => line.trim())
      .filter((line) => line)
      .map((line, index) => ({
        index,
        id: line.startsWith("http") ? line.match(/\d+/)?.[0] || line : line,
        url: line.startsWith("http")
          ? line
          : `https://item.jd.com/${line}.html`,
        status: "pending" as const,
      }));
  };

  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      if (event.data?.type === "jd_category_result") {
        console.log("handleMessage>>>>", event.data);
        const { success, category, name, error, url } = event.data.data;
        const categoryArr = category?.split(">") || [];
        setCategories((prev) =>
          prev.map((cat: any) =>
            cat.url.split("?")[0] === url
              ? {
                  ...cat,
                  name,
                  category1: categoryArr[0] ?? "",
                  category2: categoryArr[1] ?? "",
                  category3: categoryArr[2] ?? "",
                  status: success ? "success" : "error",
                  message: success ? undefined : error || "获取分类失败",
                }
              : cat
          )
        );
        promiseRef.current?.resolve(true);
      }
    };

    window.addEventListener("message", handleMessage);
    return () => window.removeEventListener("message", handleMessage);
  }, []);

  const handleQuery = async () => {
    if (!productIds.trim()) {
      message.warning("请输入商品ID或链接");
      return;
    }

    const items = parseInput(productIds);
    setCategories(items);
    setLoading(true);

    const currentTabId = await sendMessage("adq_get_tab_id", null);

    try {
      for (const item of items) {
        const previewUrl = `${item.url}?adq_preview=1`;
        promiseRef.current = Promise.withResolvers();
        const childWindow = window.open(previewUrl, "_blank");
        // 每次打开新窗口后等待一段时间，避免浏览器拦截
        await promiseRef.current.promise;
        promiseRef.current = null;

        childWindow?.close();
      }
    } catch (error) {
      message.error("查询商品分类时发生错误");
    } finally {
      setLoading(false);
      // 运行完成后将当前标签页激活
      sendMessage("adq_set_active_tab", { tabId: currentTabId });
    }
  };

  return (
    <Modal
      title="从广告迁移创意"
      open={open}
      onCancel={() => setOpen(false)}
      onOk={() => setOpen(false)}
      okText="确定"
      cancelText="取消"
    >
      <div style={{ padding: "0 12px" }}>
        <Space direction="vertical" style={{ width: "100%", height: "100%" }}>
          <p style={{ margin: "4px 0" }}>
            <span>批量提取京东商品的商品分类, 需注意:</span>
            <ul>
              <li>1. 会多次打开新标签页, 在执行过程中请勿操作浏览器</li>
              <li>2. 首次打开京东页面可能会需要登录或验证</li>
              <li>3. 若出错或卡住可以刷新页面重新开始</li>
            </ul>
          </p>
          <Input.TextArea
            value={productIds}
            onChange={handleInputChange}
            placeholder="请输入商品ID或链接，每行一个"
            rows={4}
          />
          <Space>
            <Button
              type="primary"
              onClick={handleQuery}
              loading={loading}
              size="small"
            >
              执行
            </Button>
            <Button
              onClick={handleExport}
              size="small"
              disabled={!categories.length}
            >
              导出Excel
            </Button>
          </Space>
          <Table
            dataSource={categories}
            scroll={{ y: 300 }}
            columns={columns}
            rowKey="index"
            size="small"
            pagination={false}
            loading={loading}
            style={{ flex: 1 }}
          />
        </Space>
      </div>
    </Modal>
  );
};
