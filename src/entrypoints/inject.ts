import { mainToContentMessenger } from "@/messageMainWorld";

export default defineUnlistedScript(() => {
  const watch_urls = {
    // 广告 start
    addCreative: "api/v3.0/dynamic_creatives/add", // 新建创意
    updateDC: "/api/v3.0/dynamic_creatives/update", // 更新创意
    deleteCreative: "api/v3.0/dynamic_creatives/delete", // 删除创意
    addAd: "/api/v3.0/adgroups/add", // 新建广告组
    deleteAd: "/api/v3.0/adgroups/delete", // 删除广告组
    updateAd: "/v3.0/adgroups/update", // 编辑广告
    updateAdStatus: "/api/v3.0/batch_async_requests/add", // 列表批量更新广告状态
    configuredStatus: "/api/v3.0/adgroup_configured_status/update", // 列表更新广告 [启用开关]
    // 广告 end
    // 热云 start
    reyunMaterialList: "/cas/api/v2/imagevideo/search", // 热云素材列表
    // 热云 end
  };

  const black_urls = ["aegis.qq.com/collect"];

  console.log("inject xhr script");

  // 解析并修改请求的body参数
  function $$modifyRequestBody(body: string, url, method) {
    let bodyObj, isAdq;
    try {
      if (!body.startsWith("{") && !body.startsWith("["))
        return { body, bodyObj, isAdq };
      bodyObj = JSON.parse(body);
      // 修改body对象
      // if (bodyObj.__adq__) {
      //   delete bodyObj.__adq__;
      //   isAdq = true;
      // }
      body = JSON.stringify(bodyObj);
    } catch (e) {
      console.log("Failed to parse request:", method, url);
      console.log("Failed to parse request body:", body);
    }
    return { body, bodyObj, isAdq };
  }

  /**
   * 接口请求前处理
   * @return {} boolean; // true: 继续下一步(发起请求); false: 拦截请求
   */
  async function handleBeforeRequest({ url, params, isAdq }) {
    return true;
    // let match_url_key = Object.keys(watch_urls).find((key) =>
    //   url.includes(watch_urls[key]) ? key : null
    // )
    // if (!match_url_key) return true

    // switch (match_url_key) {
    //   case "updateDC":
    //     return isInteceptUpdateCreative(bodyObj)
    //   default:
    //     return true
    // }
  }

  // 当请求完成后
  function handleAfterResponse({
    url,
    params,
    isAdq,
    fetchResponse = null,
    xhrResponse = null,
  }) {
    let match_url_key = Object.keys(watch_urls).find((key) =>
      url.includes(watch_urls[key]) ? key : null
    );

    if (!match_url_key) return;

    let res = null;
    if (xhrResponse) {
      try {
        res = JSON.parse(xhrResponse);
      } catch (error) {
        console.log("Failed to parse xhr response:", error);
      }
    }
    if (fetchResponse) {
      res = fetchResponse;
      console.log("fetch response:", res);
    }

    switch (match_url_key) {
      case "addAd": {
        // mainWorldMessaging.sendToBackground({
        //   type: "send_operation_log",
        //   data: { operation_type: "addAdGroup", data: params },
        // });
        mainToContentMessenger.sendMessage('forwardToBackground', {
          action: 'send_operation_log',
          params: { operation_type: "addAdGroup", data: params }
        })
        break;
      }
      case "deleteAd": {
        // mainWorldMessaging.sendToBackground({
        //   type: "send_operation_log",
        //   data: { operation_type: "deleteAdGroup", data: params },
        // });
        mainToContentMessenger.sendMessage('forwardToBackground', {
          action: 'send_operation_log',
          params: { operation_type: "deleteAdGroup", data: params }
        })
      }
      case "updateAd": {
        if (!params) return;
        // mainWorldMessaging.sendToBackground({
        //   type: "send_operation_log",
        //   data: { operation_type: "updateAdGroup", data: params },
        // });
        mainToContentMessenger.sendMessage('forwardToBackground', {
          action: 'send_operation_log',
          params: { operation_type: "updateAdGroup", data: params }
        })
        break;
      }
      case "updateAdStatus": {
        // mainWorldMessaging.sendToBackground({
        //   type: "send_operation_log",
        //   data: { operation_type: "batchUpdateAdGroup", data: params },
        // });
        mainToContentMessenger.sendMessage('forwardToBackground', {
          action: 'send_operation_log',
          params: { operation_type: "batchUpdateAdGroup", data: params }
        })
        break;
      }
      case "configuredStatus": {
        // mainWorldMessaging.sendToBackground({
        //   type: "send_operation_log",
        //   data: { operation_type: "configuredStatusAdGroup", data: params },
        // });
        mainToContentMessenger.sendMessage('forwardToBackground', {
          action: 'send_operation_log',
          params: { operation_type: "configuredStatusAdGroup", data: params }
        })
        break;
      }
      case "addCreative": {
        // mainWorldMessaging.sendToBackground({
        //   type: "send_operation_log",
        //   data: { operation_type: "addCreative", data: params },
        // });
        mainToContentMessenger.sendMessage('forwardToBackground', {
          action: 'send_operation_log',
          params: { operation_type: "addCreative", data: params }
        })
        break;
      }
      case "updateDC": {
        // 转交编辑创意页面处理
        console.log("updateDC", params);
        mainToContentMessenger.sendMessage('updateCreativeRequest', res)
        break;
      }
      case "deleteCreative": {
        // mainWorldMessaging.sendToBackground({
        //   type: "send_operation_log",
        //   data: { operation_type: "deleteCreative", data: params },
        // });
        mainToContentMessenger.sendMessage('forwardToBackground', {
          action: 'send_operation_log',
          params: { operation_type: "deleteCreative", data: params }
        })
        break;
      }

      case "reyunMaterialList": {
        console.log("reyunMaterialList", res);
        mainToContentMessenger.sendMessage('get_reyun_material_list', res)
      }
    }
  }

  function is_black_url(url) {
    return black_urls.some((black_url) => url.includes(black_url));
  }

  {
    var XHR = XMLHttpRequest.prototype;
    var originOpen = XHR.open;
    var originSend = XHR.send;

    // 对open进行patch 获取url和method
    XHR.open = function (method, url) {
      //@ts-ignore
      this._method = method;
      //@ts-ignore
      this._url = url;
      //@ts-ignore
      return originOpen.apply(this, arguments);
    };
    // 同send进行patch 获取responseData.
    XHR.send = async function (requestBody, ...args) {
      let isAdq = false,
        params;

      // let match_url_key = Object.keys(watch_urls).find((key) =>
      //   this._url.includes(watch_urls[key]) ? key : null
      // )
      // if (match_url_key === "updateDC") {
      //   setTimeout(() => {
      //     debugger
      //   }, 300)
      // }

      //@ts-ignore
      if (is_black_url(this._url))
        return originSend.apply(this, [requestBody, ...args]);

      // 您可以在这里修改请求参数
      if (requestBody) {
        const res = $$modifyRequestBody(
          requestBody as string,
          //@ts-ignore
          this._url,
          //@ts-ignore
          this._method
        );
        requestBody = res.body;
        isAdq = res.isAdq;
        params = res.bodyObj;
      }

      //@ts-ignore
      if (!(await handleBeforeRequest({ url: this._url, params, isAdq })))
        return;

      this.addEventListener(
        "load",
        function (..._args) {
          if (
            (this.responseType === "text" || this.responseType === "") &&
            this.responseText
          ) {
            handleAfterResponse({
              //@ts-ignore
              url: this._url,
              params,
              isAdq,
              xhrResponse: this.response,
            });
          }
        },
        { once: true }
      );
      return originSend.apply(this, [requestBody, ...args]);
    };
  }
});
