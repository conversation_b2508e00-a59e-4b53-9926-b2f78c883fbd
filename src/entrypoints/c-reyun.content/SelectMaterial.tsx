import { ContentScriptContext } from "#imports";
import { CheckCircleOutlined } from "@ant-design/icons";
import { Button, Tooltip } from "antd";
import { createRoot } from "react-dom/client";
import { useEffect } from "react";

// 自定义素材列表的类名
const ADD_MATERIAL_WRAP_CLASS = "adq_material_wrap";
// 素材列表的类名
// const MATERIAL_WRAP_CLASS =
//   "grid-cols-\\[repeat\\(auto-fill\\,_minmax\\(276px\\,_1fr\\)\\)\\]";
const MATERIAL_WRAP_CLASS = '.grid.gap-2:has(.bg-white.rounded .left-conner)'

export const selectIconMount = async (ctx: ContentScriptContext) => {
  return createIntegratedUi(ctx, {
    position: "inline",
    anchor: MATERIAL_WRAP_CLASS,
    onMount: (container: Element) => {
      const div = document.createElement("div");
      createRoot(div).render(<Port />);
      document.body.appendChild(div);
      container.parentElement!.classList.add(ADD_MATERIAL_WRAP_CLASS);

      const token = localStorage.getItem('token')
      storage.setItem('local:reyun_token', token)
    },
  });
};

export const SelectIcon = ({index}) => {
  const [selected, setSelected] = useState<boolean>(false);
  return (
    <Tooltip title="选择该创意">
      <div className={`w-8 h-8 rounded-full bg-pink-500/40 flex justify-center items-center text-white hover:bg-pink-500 transition duration-300 absolute top-6 right-3 cursor-pointer ${selected ? '!bg-pink-500 adq_material_selected' : ''}`} data-index={index} onClick={() => setSelected(!selected)}>
      <CheckCircleOutlined size={36} />
      </div>
    </Tooltip>
  );
};

// 中转节点
export const Port = () => {
  const watchItem = () => {
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === "childList") {
          setTimeout(() => handleItemChange(), 500);
        }
      });
    });
    observer.observe(document.querySelector(MATERIAL_WRAP_CLASS)!, {
      childList: true,
      subtree: false,
      attributes: false,
      characterData: false,
    });
  };

  const handleItemChange = () => {
    const materialItem = document.querySelectorAll(
      `.${ADD_MATERIAL_WRAP_CLASS}>div`
    );
    materialItem.forEach((item, index) => {
      item.classList.add("adq_material_item");
      item.classList.add("relative");
      if (item.querySelector(".adq_material_item_icon")) return;
      const icon = document.createElement("div");
      createRoot(icon).render(<SelectIcon index={index} />);
      item!.appendChild(icon);
    });
  };

  useEffect(() => {
    watchItem();
    handleItemChange();
  }, []);
  return <></>;
};
