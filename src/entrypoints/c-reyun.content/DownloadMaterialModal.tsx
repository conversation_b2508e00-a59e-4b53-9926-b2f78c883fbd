import { ContentScriptContext } from "#imports";
import { onMessage } from "@/message";
import { mainToContentMessenger } from "@/messageMainWorld";
import { useStore } from "@/store";
import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  DownloadOutlined,
  PoweroffOutlined,
  WarningOutlined,
} from "@ant-design/icons";
import { Modal, Table, Image, Space, Button } from "antd";
import dayjs from "dayjs";
import { createRoot } from "react-dom/client";
import { toast, Toaster } from "sonner";
import * as XLSX from "xlsx";

export const downloadMaterialMount = async (ctx: ContentScriptContext) => {
  return createIntegratedUi(ctx, {
    position: "inline",
    anchor: `body`,
    onMount: (container: Element) => {
      const div = document.createElement("div");
      createRoot(div).render(<DownloadMaterial />);
      container.appendChild(div);
    },
  });
};

export const DownloadMaterial = () => {
  // const [materialList, setMaterialList] = useState<any[]>([]);
  const [open, setOpen] = useState(false);
  const [selected, setSelected] = useState<any[]>([]);
  const materialListRef = useRef<any[]>([]);
  const [downloadLoading, setDownloadLoading] = useState(false);
  const account = useStore((store) => store.account);

  useEffect(() => {
    console.log(2222, account);
  }, [account]);

  useEffect(() => {
    mainToContentMessenger.onMessage(
      "get_reyun_material_list",
      ({ data }: any) => {
        console.log(222, "get_reyun_material_list", data);
        // setMaterialList(data.data?.list);
        materialListRef.current = data.data?.list;
      }
    );
  }, []);

  useEffect(() => {
    onMessage("adq_reyun_show_download_material_modal", (data: any) => {
      showModal();
    });
  }, []);

  const showModal = useCallback(async () => {
    const selectedElement = getSelectedElement();
    console.log(account);
    if (!account?.success) return toast.error(`暂无权限: ${account?.message}`);
    if (!selectedElement.length) return toast.error("请先选择素材");

    setOpen(true);

    const selected = selectedElement.map((idx) => materialListRef.current[idx]);
    console.log(333, selected);
    setSelected(selected);
  }, [account]);

  const getSelectedElement = () => {
    return Array.from(document.querySelectorAll(".adq_material_selected")).map(
      //@ts-ignore
      (item) => item.dataset.index
    );
  };

  // 获取文件拓展名
  const getUrlExt = (url: string) => {
    return url.split("?")[0].split(".").pop();
  };

  const downloadItem = async (
    item,
    index,
    root: FileSystemDirectoryHandle | null = null
  ) => {
    try {
      if (!root) root = await window.showDirectoryPicker();
      let _selected = [...selected];
      item._status = "下载中";
      const title = item.title.replace(/<[^>]+>/g, "");
      const describe = item.describe.replace(/<[^>]+>/g, "");
      _selected[index] = { ...item };
      setSelected(_selected);

      let success = 0,
        fail = 0;

      // 处理图片下载
      if (item.imageUrl) {
        for (const url of item.imageUrl) {
          try {
            const ext = getUrlExt(url);
            const filename = `${title}-${item.id}-${describe}.${ext}`;
            const { arrayBuffer } = await getArrayBuffurFromRemoteUrl(url);
            const fileHandle = await root.getFileHandle(filename, {
              create: true,
            });
            const writable = await fileHandle.createWritable();
            await writable.write(arrayBuffer as ArrayBuffer);
            await writable.close();
            success++;
          } catch (error) {
            console.error(`下载图片失败: ${url}`, error);
            toast.error(`下载图片失败: ${url}`);
            fail++;
          }
        }
      }

      // 处理视频下载
      if (item.videoUrl) {
        try {
          const ext = getUrlExt(item.videoUrl);
          const filename = `${title}-${item.id}-${describe}.${ext}`;
          const { arrayBuffer } = await getArrayBuffurFromRemoteUrl(
            item.videoUrl
          );
          const fileHandle = await root.getFileHandle(filename, {
            create: true,
          });
          const writable = await fileHandle.createWritable();
          await writable.write(arrayBuffer as ArrayBuffer);
          await writable.close();
          success++;
        } catch (error) {
          console.error(`下载视频失败: ${item.videoUrl}`, error);
          toast.error(`下载视频失败: ${item.videoUrl}`);
          fail++;
        }
      }
      if (success && !fail) item._status = "下载成功";
      if (!success && fail) item._status = "下载失败";
      if (success && fail) item._status = "部分下载成功";
      _selected[index] = { ...item };
      setSelected(_selected);
    } catch (error) {
      console.error(`下载素材失败: ${item.title}`, error);
      toast.error(`下载素材失败: ${item.title}`);
    }
  };

  const handleDownloadAll = async () => {
    setDownloadLoading(true);

    if (!window.showDirectoryPicker)
      return alert("当前浏览器不支持该功能, 推荐使用chrome或Edge浏览器");

    const root = await window.showDirectoryPicker();
    const _selected = [...selected];

    for (let i = 0; i < _selected.length; i++) {
      await downloadItem(_selected[i], i, root);
    }

    // 创建并保存 Excel 文件
    try {
      const ws = XLSX.utils.json_to_sheet(selected);
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, "素材列表");
      const excelData = XLSX.write(wb, { type: "array", bookType: "xlsx" });
      const blob = new Blob([excelData], { type: "application/octet-stream" });

      const fileHandle = await root.getFileHandle(
        `热云素材下载汇总表_${dayjs().format("YYYY-MM-DD_hh-mm-ss")}.xlsx`,
        { create: true }
      );
      const writable = await fileHandle.createWritable();
      await writable.write(blob);
      await writable.close();

      toast.success("下载完成");
    } catch (error) {
      console.error("创建汇总表失败:", error);
      toast.error("创建汇总表失败");
    }
    setDownloadLoading(false);
  };

  return (
    <div>
      <Modal
        title="热云-批量下载素材"
        open={open}
        onCancel={() => setOpen(false)}
        onOk={() => handleDownloadAll()}
        okText="全部下载"
        cancelText="取消"
        width={1000}
        okButtonProps={{
          loading: downloadLoading,
        }}
      >
        <Table
          key="id"
          rowKey="id"
          dataSource={selected}
          scroll={{ x: 1000 }}
          columns={[
            {
              title: "名称",
              dataIndex: "title",
              ellipsis: { showTitle: true },
              width: 160,
              render: (text) => (
                <div dangerouslySetInnerHTML={{ __html: text }}></div>
              ),
            },
            {
              title: "描述",
              dataIndex: "describe",
              ellipsis: { showTitle: true },
              width: 240,
              render: (text) => (
                <div dangerouslySetInnerHTML={{ __html: text }}></div>
              ),
            },
            {
              title: "ID",
              dataIndex: "id",
              ellipsis: { showTitle: true },
              width: 200,
            },
            // {
            //   title: "封面",
            //   dataIndex: "converUrl",
            //   render: (text) => <Image src={text} width={40} />,
            // },
            {
              title: "图片",
              dataIndex: "imageUrl",
              render: (value) => (
                <Space>
                  {value.map((item: string) => (
                    <Image src={item} width={40} />
                  ))}
                </Space>
              ),
            },
            {
              title: "视频",
              dataIndex: "videoUrl",
              render: (text) => <video src={text} width={40} />,
            },
            {
              title: "状态",
              dataIndex: "_status",
              width: 100,
              render: (text, record, index) => {
                if (!text)
                  return (
                    <Button
                      type="link"
                      icon={<DownloadOutlined />}
                      onClick={() => downloadItem(record, index)}
                    />
                  );
                if (text === "下载中")
                  return (
                    <Button type="link" icon={<PoweroffOutlined />} loading>
                      下载中
                    </Button>
                  );
                if (text === "下载成功")
                  return (
                    <Button
                      color="cyan"
                      variant="link"
                      icon={<CheckCircleOutlined />}
                    >
                      下载成功
                    </Button>
                  );
                if (text === "下载失败")
                  return (
                    <Button
                      color="danger"
                      variant="link"
                      icon={<CloseCircleOutlined />}
                    >
                      下载失败
                    </Button>
                  );
                if (text === "部分下载成功")
                  return (
                    <Button
                      color="pink"
                      variant="link"
                      icon={<WarningOutlined />}
                    >
                      部分下载成功
                    </Button>
                  );
              },
            },
          ]}
        ></Table>
      </Modal>
      <Toaster position="top-right" />
    </div>
  );
};
