export const soLandingMount = async (ctx) => {
  return createIntegratedUi(ctx, {
    position: "overlay",
    anchor: "body",
    onMount: async (container: Element) => {
      const originUrls = await storage.getItem(
        "local:so_jd_tencent_landing_origin_urls"
      );
      const response = await fetch(
        "https://so.jd.com/landingPageConfig/batchGenerateLandingPage",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            channel: "UNION",
            mediaList: ["TX"],
            landingPageType: "DEFAULT",
            targetUrls: originUrls,
            targetUrlFile: [],
            mediaVersion: "TX3",
            pageSize: (originUrls as string[]).length,
          }),
        }
      );
      const result = await response.json();
      console.log('>>>>landing content', result);
      window.opener.postMessage(
        {
          type: "get_so_jd_tencent_landing_result",
          data: result,
        }, '*'
      )
      return result
    },
  });
};
