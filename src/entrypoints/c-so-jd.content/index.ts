import "@/assets/styles/tailwind.css"
import { soLandingMount } from "./soLanding";
// *://item.jd.com/*?adq_preview=*
export default defineContentScript({
  // matches: ['*://so.jd.com/systemSetting-page/linkTool/landingPage?adq_preview=*'],
  matches: ['*://so.jd.com/systemSetting-page/linkTool/landingPage'],
  cssInjectionMode: "manifest",
  async main(ctx) {
    console.log(12132312, "so landing script loaded");
    const soLandingUI = await soLandingMount(ctx);
    soLandingUI.mount()
  },
});
