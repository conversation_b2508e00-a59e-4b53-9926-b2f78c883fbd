// 在主世界和 content script 中共同引入此文件
// messaging/page-protocol.ts
import { defineWindowMessaging } from '@webext-core/messaging/page';

// 定义消息类型（主世界 → content）
interface MainToContentProtocol {
  forwardToBackground: (data?: {action: string, params?:any}) => any;

  updateCreativeRequest: (data?:any) => any; 
  get_reyun_material_list: (data?:any) => any; 
}

// 创建窗口通信实例（命名空间需唯一）
export const mainToContentMessenger = defineWindowMessaging<MainToContentProtocol>({
  namespace: `my-extension-adq-helper`, // 使用扩展 ID 确保唯一性
});