import { message, TreeSelect } from "antd"

import { getPoiList } from "@/entrypoints/c-tencent.content/handles/packages/poi"
import { getTargetingList, getTargetSlot } from "@/entrypoints/c-tencent.content/handles/targeting"

import { DynamicFormItemType, type DynamicFormItemProps } from "../dynamic.d"
import { useStore } from "@/store"

export const updateAdModel: Record<string, DynamicFormItemProps> = {
  "targeting.geo_location.regions": {
    label: "地域",
    type: DynamicFormItemType.TREE_SELECT,
    props: {
      treeCheckable: true,
      showCheckedStrategy: TreeSelect.SHOW_PARENT,
      placeholder: '请选择地域'
    },
    fetchDataSource: async () => {
      const targetingSlot = await getTargetSlot()
      const regionTree =
        targetingSlot?.geo_location?.options?.geo_location?.list
      if (!regionTree) {
        message.error("未获取到地域数据")
        return []
      }
      const res = regionTree.map((province) => ({
        title: province.desc,
        value: province.value,
        children: province.options?.map((city) => ({
          title: city.desc,
          value: city.value,
          children: city.options?.map((area) => ({
            title: area.desc,
            value: area.value,
            children: area.options?.map((area1) => ({
              title: area1.desc,
              value: area1.value
            }))
          }))
        }))
      }))
      return res
    }
  },
  "targeting.age": {
    label: "年龄",
    type: DynamicFormItemType.AGE_RANGE,
    props: {
      min: 14,
      max: 66,
      placeholder: '请选择年龄'
    }
  },
  "targeting.gender": {
    label: "性别",
    type: DynamicFormItemType.SELECT,
    props: {
      options: [
        { label: "不限", value: 0 },
        { label: "男", value: '["MALE"]' },
        { label: "女", value: '["FEMALE"]' }
      ],
      placeholder: '请选择性别'
    }
  },
  targeting_id: {
    label: "定向包",
    type: DynamicFormItemType.SELECT,
    props: {
      options: [],
      placeholder: '请选择定向包'
    },
    fetchDataSource: async () => {
      const targetingList = await getTargetingList()
      return targetingList.map((item) => ({
        label: item.targeting_name + "(" + item.targeting_translation + ")",
        value: item.targeting_id
      }))
    }
  },
  poi_list: {
    label: "门店",
    type: DynamicFormItemType.TREE_SELECT,
    props: {
      treeCheckable: true,
      placeholder: '请选择门店'
    },
    fetchDataSource: async () => {
      const poiList = await getPoiList()
      const account = useStore.getState().account
      return [
        {
          title: "全部",
          value: "all",
          children: poiList.map((item) => ({
            title:
              item.local_store_name +
              `(${item.local_store_province}${item.local_store_city}${item.local_store_address})`,
            value: item.poi_id + "@" + account?.data!.account_id
          }))
        }
      ]
    }
  }
}
