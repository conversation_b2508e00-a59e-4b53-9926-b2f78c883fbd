import { QuestionCircleOutlined } from "@ant-design/icons"
import { Checkbox, InputNumber, Space, Tooltip } from "antd"
import React, { useEffect, useState } from "react"

export const AgeRangeItem = (props: any) => {
  const { type, source, onChange, ...rest } = props
  const [_value, setValue] = useState<[number, number] | null>(props.value || [null, null])
  const [prev, setPrev] = useState<[number, number] | null>(_value)

  const handleChange = (value: [number, number] | null) => {
    setPrev(_value ? [..._value] : null)
    setValue(value)
    onChange(value ? [{ min: value[0], max: value[1] }] : null)
  }

  return (
    <Space style={{ display: "flex", gap: 8, alignItems: "center" }}>
      <Checkbox
        checked={_value === null}
        onChange={(e) => {
          handleChange(e.target.checked ? null : prev)
        }}>
        不限
      </Checkbox>
      {_value !== null && (
        <div>
          <InputNumber
            min={props.min}
            max={props.max}
            controls={false}
            value={_value[0]}
            placeholder="最小值"
            onChange={(value) => handleChange([value, _value[1]])}
          />
          <span>-</span>
          <InputNumber
            min={props.min}
            max={props.max}
            controls={false}
            value={_value[1]}
            placeholder="最大值"
            onChange={(value) => handleChange([_value[0], value])}
          />
          <span>岁{_value[1] == 66 ? "及以上" : ""}</span>
        </div>
      )}
      {props.help && (
        <Tooltip title={props.help}>
          <QuestionCircleOutlined style={{ marginLeft: 8 }} />
        </Tooltip>
      )}
    </Space>
  )
}
