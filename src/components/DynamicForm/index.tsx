import { Input, Select, TreeSelect } from "antd"
import React, { useEffect, useState } from "react"
import type { Component, CSSProperties, ReactNode } from "react"

import type { DynamicFormItemProps, DynamicFormItemType } from "./dynamic"
import { AgeRangeItem } from "./item/AgeRangeItem"

export const DynamicForm = (
  props: Omit<DynamicFormItemProps, "label"> & {
    style?: CSSProperties,
    onChange?: (value: any, extra?: any) => void
  }
) => {
  const comp: Record<DynamicFormItemType, React.ComponentType> = {
    input: Input,
    select: Select,
    range: () => null,
    tree_select: TreeSelect,
    age_range: AgeRangeItem
  }
  const { type, source, fetchDataSource, props: _props, extra, ...rest } = props
  const [loading, setLoading] = useState(false)
  const [_dataSource, setDataSource] = useState<any>(source)
  const Comp = comp[type]

  const onChange = (value: any) => {
    props.onChange?.(value, extra)
  }

  useEffect(() => {
    if (fetchDataSource) {
      setLoading(true)
      setDataSource(null)
      fetchDataSource()
        .then((res) => {
          setDataSource(res)
        })
        .finally(() => {
          setLoading(false)
        })
    }
  }, [type])

  return (
    <Comp
      source={_dataSource}
      treeData={_dataSource}
      options={_dataSource}
      loading={loading}
      {..._props}
      {...rest}
      onChange={onChange}
    />
  )
}
