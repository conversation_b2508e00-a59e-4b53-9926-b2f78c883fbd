import { sendMessage } from "@/message";
import dayjs from "dayjs";
import { nanoid } from "nanoid";
import { ReactNode } from "react";
import { DataDiff } from "../DataDiff";

export interface LogItem {
  type: "info" | "success" | "error" | "diff";
  key: string;
  task_id: string;
  message?: string;
  time?: string;
  data?: any
}

interface LogListProps {
  logs: LogItem[];
  filter?: (log: LogItem) => boolean;
}

interface LogDataOptions {
  extra?: any;
  interceptor?: (log: LogItem, logs: LogItem[], extra?: any) => LogItem;
  autoSend?: boolean;
}

export class LogData {
  logs: LogItem[] = [];
  task_id: string = "";
  _interceptor?: LogDataOptions["interceptor"];
  extra?: any = null;
  _autoSend: boolean = true;

  constructor(options?: LogDataOptions) {
    this.logs = [];
    this.task_id = nanoid();
    const { interceptor, extra, autoSend = true } = options || {};
    this._interceptor = interceptor;
    this.extra = extra;
    this._autoSend = autoSend;
  }

  private facotry(
    log: Omit<LogItem, "key" | "task_id" | "type">,
    type: LogItem["type"]
  ): LogItem {
    return {
      type: type,
      key: nanoid(),
      task_id: this.task_id,
      message: log.message,
      data: log.data,
      time: dayjs().format("HH:mm:ss"), // 2021-01-01 00:00:00
    };
  }

  private sendMessage(log: LogItem) {
    sendMessage("adq_update_log_sender", {
      logs: this.logs,
      log,
    });
  }

  private add(log: LogItem) {
    log = this._interceptor
      ? this._interceptor?.(log, this.logs, this.extra)
      : log;
    this.logs.push(log);
    this._autoSend && this.sendMessage(log);
  }

  info(message: string) {
    this.add(this.facotry({ message }, "info"));
  }

  success(message: string) {
    this.add(this.facotry({ message }, "success"));
  }
  error(message: string) {
    this.add(this.facotry({ message }, "error"));
  }
  diff(source: any, target: any) {
    this.add(this.facotry({ data: { source, target } }, "diff"));
  }

  start(str?: string) {
    this.info(`----- 开始执行: ${str || ""} ------`);
  }
  end(str?: string) {
    this.info(`----- 执行完成: ${str || ""} ------`);
  }
  updateExt(ext: any) {
    // this.extra = { ...this.extra, ...ext };
    Object.assign(this.extra, ext);
  }
}

export const LogList = (props: LogListProps) => {
  const { logs, filter } = props;

  useEffect(() => {
    if (logs && logs.length > 0) {
      const container = document.getElementById("log-container");
      if (container) {
        container.scrollTop = container.scrollHeight;
      }
    }
  }, [logs]);

  return (
    <div className="text-[14px] leading-[1.5]">
      {logs.map((log, index) => {
        if (filter && !filter(log)) return null;
        return <LogItem key={log.key} log={log} />;
      })}
    </div>
  );
};

interface LogItemProps {
  log: LogItem;
}
const LogItem = (props: LogItemProps) => {
  const { type, message, time, data } = props.log;

  if (type === "diff") return <DataDiff source={data.source} target={data.target} />;

  if (type === "info")
    return (
      <p>
        [{time}]: {message}
      </p>
    );

  if (type === "success")
    return (
      <p style={{ color: "green" }}>
        [{time}]: {message}
      </p>
    );

  if (type === "error")
    return (
      <p style={{ color: "red" }}>
        [{time}]: {message}
      </p>
    );
  return null;
};
