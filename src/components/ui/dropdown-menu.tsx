import * as React from "react"
import * as DropdownMenuPrimitive from "@radix-ui/react-dropdown-menu"
import { CheckIcon, ChevronRightIcon, CircleIcon } from "lucide-react"

import { cn } from "@/lib/utils"

function DropdownMenu({
  ...props
}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {
  return <DropdownMenuPrimitive.Root data-slot="dropdown-menu" {...props} />
}

function DropdownMenuPortal({
  ...props
}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {
  return (
    <DropdownMenuPrimitive.Portal data-slot="dropdown-menu-portal" {...props} />
  )
}

function DropdownMenuTrigger({
  ...props
}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {
  return (
    <DropdownMenuPrimitive.Trigger
      data-slot="dropdown-menu-trigger"
      {...props}
    />
  )
}

function DropdownMenuContent({
  className,
  sideOffset = 4,
  ...props
}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {
  return (
    <DropdownMenuPrimitive.Portal>
      <DropdownMenuPrimitive.Content
        data-slot="dropdown-menu-content"
        sideOffset={sideOffset}
        className={cn(
          "adq-bg-popover adq-text-popover-foreground data-[state=open]:adq-animate-in data-[state=closed]:adq-animate-out data-[state=closed]:adq-fade-out-0 data-[state=open]:adq-fade-in-0 data-[state=closed]:adq-zoom-out-95 data-[state=open]:adq-zoom-in-95 data-[side=bottom]:adq-slide-in-from-top-2 data-[side=left]:adq-slide-in-from-right-2 data-[side=right]:adq-slide-in-from-left-2 data-[side=top]:adq-slide-in-from-bottom-2 adq-z-50 adq-max-h-(--radix-dropdown-menu-content-available-height) adq-min-w-[8rem] adq-origin-(--radix-dropdown-menu-content-transform-origin) adq-overflow-x-hidden adq-overflow-y-auto adq-rounded-md adq-border adq-p-1 adq-shadow-md",
          className
        )}
        {...props}
      />
    </DropdownMenuPrimitive.Portal>
  )
}

function DropdownMenuGroup({
  ...props
}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {
  return (
    <DropdownMenuPrimitive.Group data-slot="dropdown-menu-group" {...props} />
  )
}

function DropdownMenuItem({
  className,
  inset,
  variant = "default",
  ...props
}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {
  inset?: boolean
  variant?: "default" | "destructive"
}) {
  return (
    <DropdownMenuPrimitive.Item
      data-slot="dropdown-menu-item"
      data-inset={inset}
      data-variant={variant}
      className={cn(
        "focus:adq-bg-accent focus:adq-text-accent-foreground data-[variant=destructive]:adq-text-destructive data-[variant=destructive]:focus:adq-bg-destructive/10 dark:data-[variant=destructive]:focus:adq-bg-destructive/20 data-[variant=destructive]:focus:adq-text-destructive data-[variant=destructive]:*:[svg]:!adq-text-destructive [&_svg:not([class*='text-'])]:adq-text-muted-foreground adq-relative adq-flex adq-cursor-default adq-items-center adq-gap-2 adq-rounded-sm adq-px-2 adq-py-1.5 adq-text-sm adq-outline-hidden adq-select-none data-[disabled]:adq-pointer-events-none data-[disabled]:adq-opacity-50 data-[inset]:adq-pl-8 [&_svg]:adq-pointer-events-none [&_svg]:adq-shrink-0 [&_svg:not([class*='size-'])]:adq-size-4",
        className
      )}
      {...props}
    />
  )
}

function DropdownMenuCheckboxItem({
  className,
  children,
  checked,
  ...props
}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {
  return (
    <DropdownMenuPrimitive.CheckboxItem
      data-slot="dropdown-menu-checkbox-item"
      className={cn(
        "focus:adq-bg-accent focus:adq-text-accent-foreground adq-relative adq-flex adq-cursor-default adq-items-center adq-gap-2 adq-rounded-sm adq-py-1.5 adq-pr-2 adq-pl-8 adq-text-sm adq-outline-hidden adq-select-none data-[disabled]:adq-pointer-events-none data-[disabled]:adq-opacity-50 [&_svg]:adq-pointer-events-none [&_svg]:adq-shrink-0 [&_svg:not([class*='size-'])]:adq-size-4",
        className
      )}
      checked={checked}
      {...props}
    >
      <span className="adq-pointer-events-none adq-absolute adq-left-2 adq-flex adq-size-3.5 adq-items-center adq-justify-center">
        <DropdownMenuPrimitive.ItemIndicator>
          <CheckIcon className="adq-size-4" />
        </DropdownMenuPrimitive.ItemIndicator>
      </span>
      {children}
    </DropdownMenuPrimitive.CheckboxItem>
  )
}

function DropdownMenuRadioGroup({
  ...props
}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {
  return (
    <DropdownMenuPrimitive.RadioGroup
      data-slot="dropdown-menu-radio-group"
      {...props}
    />
  )
}

function DropdownMenuRadioItem({
  className,
  children,
  ...props
}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {
  return (
    <DropdownMenuPrimitive.RadioItem
      data-slot="dropdown-menu-radio-item"
      className={cn(
        "focus:adq-bg-accent focus:adq-text-accent-foreground adq-relative adq-flex adq-cursor-default adq-items-center adq-gap-2 adq-rounded-sm adq-py-1.5 adq-pr-2 adq-pl-8 adq-text-sm adq-outline-hidden adq-select-none data-[disabled]:adq-pointer-events-none data-[disabled]:adq-opacity-50 [&_svg]:adq-pointer-events-none [&_svg]:adq-shrink-0 [&_svg:not([class*='size-'])]:adq-size-4",
        className
      )}
      {...props}
    >
      <span className="adq-pointer-events-none adq-absolute adq-left-2 adq-flex adq-size-3.5 adq-items-center adq-justify-center">
        <DropdownMenuPrimitive.ItemIndicator>
          <CircleIcon className="adq-size-2 adq-fill-current" />
        </DropdownMenuPrimitive.ItemIndicator>
      </span>
      {children}
    </DropdownMenuPrimitive.RadioItem>
  )
}

function DropdownMenuLabel({
  className,
  inset,
  ...props
}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {
  inset?: boolean
}) {
  return (
    <DropdownMenuPrimitive.Label
      data-slot="dropdown-menu-label"
      data-inset={inset}
      className={cn(
        "adq-px-2 adq-py-1.5 adq-text-sm adq-font-medium data-[inset]:adq-pl-8",
        className
      )}
      {...props}
    />
  )
}

function DropdownMenuSeparator({
  className,
  ...props
}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {
  return (
    <DropdownMenuPrimitive.Separator
      data-slot="dropdown-menu-separator"
      className={cn("adq-bg-border adq--mx-1 adq-my-1 adq-h-px", className)}
      {...props}
    />
  )
}

function DropdownMenuShortcut({
  className,
  ...props
}: React.ComponentProps<"span">) {
  return (
    <span
      data-slot="dropdown-menu-shortcut"
      className={cn(
        "adq-text-muted-foreground adq-ml-auto adq-text-xs adq-tracking-widest",
        className
      )}
      {...props}
    />
  )
}

function DropdownMenuSub({
  ...props
}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {
  return <DropdownMenuPrimitive.Sub data-slot="dropdown-menu-sub" {...props} />
}

function DropdownMenuSubTrigger({
  className,
  inset,
  children,
  ...props
}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {
  inset?: boolean
}) {
  return (
    <DropdownMenuPrimitive.SubTrigger
      data-slot="dropdown-menu-sub-trigger"
      data-inset={inset}
      className={cn(
        "focus:adq-bg-accent focus:adq-text-accent-foreground data-[state=open]:adq-bg-accent data-[state=open]:adq-text-accent-foreground adq-flex adq-cursor-default adq-items-center adq-rounded-sm adq-px-2 adq-py-1.5 adq-text-sm adq-outline-hidden adq-select-none data-[inset]:adq-pl-8",
        className
      )}
      {...props}
    >
      {children}
      <ChevronRightIcon className="adq-ml-auto adq-size-4" />
    </DropdownMenuPrimitive.SubTrigger>
  )
}

function DropdownMenuSubContent({
  className,
  ...props
}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {
  return (
    <DropdownMenuPrimitive.SubContent
      data-slot="dropdown-menu-sub-content"
      className={cn(
        "adq-bg-popover adq-text-popover-foreground data-[state=open]:adq-animate-in data-[state=closed]:adq-animate-out data-[state=closed]:adq-fade-out-0 data-[state=open]:adq-fade-in-0 data-[state=closed]:adq-zoom-out-95 data-[state=open]:adq-zoom-in-95 data-[side=bottom]:adq-slide-in-from-top-2 data-[side=left]:adq-slide-in-from-right-2 data-[side=right]:adq-slide-in-from-left-2 data-[side=top]:adq-slide-in-from-bottom-2 adq-z-50 adq-min-w-[8rem] adq-origin-(--radix-dropdown-menu-content-transform-origin) adq-overflow-hidden adq-rounded-md adq-border adq-p-1 adq-shadow-lg",
        className
      )}
      {...props}
    />
  )
}

export {
  DropdownMenu,
  DropdownMenuPortal,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuLabel,
  DropdownMenuItem,
  DropdownMenuCheckboxItem,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuSub,
  DropdownMenuSubTrigger,
  DropdownMenuSubContent,
}
