import * as React from "react"

import { cn } from "@/lib/utils"

function Input({ className, type, ...props }: React.ComponentProps<"input">) {
  return (
    <input
      type={type}
      data-slot="input"
      className={cn(
        "file:adq-text-foreground placeholder:adq-text-muted-foreground selection:adq-bg-primary selection:adq-text-primary-foreground dark:adq-bg-input/30 adq-border-input adq-flex adq-h-9 adq-w-full adq-min-w-0 adq-rounded-md adq-border adq-bg-transparent adq-px-3 adq-py-1 adq-text-base adq-shadow-xs adq-transition-[color,box-shadow] adq-outline-none file:adq-inline-flex file:adq-h-7 file:adq-border-0 file:adq-bg-transparent file:adq-text-sm file:adq-font-medium disabled:adq-pointer-events-none disabled:adq-cursor-not-allowed disabled:adq-opacity-50 md:adq-text-sm",
        "focus-visible:adq-border-ring focus-visible:adq-ring-ring/50 focus-visible:adq-ring-[3px]",
        "aria-invalid:adq-ring-destructive/20 dark:aria-invalid:adq-ring-destructive/40 aria-invalid:adq-border-destructive",
        className
      )}
      {...props}
    />
  )
}

export { Input }
