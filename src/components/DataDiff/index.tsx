import { create } from 'jsondiffpatch';
import * as htmlFormatter from 'jsondiffpatch/formatters/html';
// import * as annotatedFormatter from 'jsondiffpatch/formatters/annotated'
import 'jsondiffpatch/formatters/styles/html.css';
import 'jsondiffpatch/formatters/styles/annotated.css';

export const DataDiff = ({source, target}) => {
  const diffRef = useRef<any>(null);
  const diffpatcher = create({
    // 自定义配置，确保属性顺序不影响比较
    // objectHash: obj => {
    //   return obj.id || JSON.stringify(obj);
    // },
    // propertyFilter: (name, context) => {
    //   return name.slice(0, 1) !== '$';
    // },
  });
  
  const delta = diffpatcher.diff(source, target);
  
  useEffect(() => {
    if (diffRef.current && delta) {
      // 使用HTML格式化器
      diffRef.current.innerHTML = htmlFormatter.format(delta);
      htmlFormatter.hideUnchanged()
      
      // 或者使用注释格式化器
      // diffRef.current.innerHTML = annotatedFormatter.format(delta);
    }
  }, [delta]);
  
  return (
    <div className='!p-3 bg-neutral-200 rounded-xl overflow-hidden'>
      <h3>前后差异:</h3>
      <div ref={diffRef} className="jsondiffpatch-delta w-full overflow-auto ml-[-12px]" />
    </div>
  );
};