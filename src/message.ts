import { defineExtensionMessaging } from '@webext-core/messaging';

interface ProtocolMap {
  //popup
  adq_tencent_show_copy_ad_multiple(data?:any): any;
  adq_tencent_start_batch_update_creative(data?:any): any;
  adq_tencent_open_edit_ad_field_modal(data?:any): any;
  adq_tencent_open_migrate_dc_modal(data?:any): any;
  adq_tencent_open_update_dc_field_modal(data?:any): any;

  // common
  // content消息中转
  forwardContent2Content(data: {action: string, params:any, tabId?:number}): any;
  adq_update_account(data: any): any;
  adq_build_connect(data: any): any;
  adq_connect_fail(data: any): any;
  adq_get_current_account(data:any): any;
  adq_update_log_sender(data: any): any;
  adq_update_log_port(data: any): any;
  adq_send_operation_log(data: any): any;
  adq_get_tab_id(data:any): any;
  adq_set_active_tab(data: {tabId: number}): any;

  // tencent
  adq_get_tencent_headers(data:any): any;
  adq_get_tencent_region(): any;
  adq_tencent_comfirm_Region(data: any): any;
  adq_jd_open_category_modal(data: any): any;

  // 热云
  adq_reyun_show_download_material_modal(data: any): any;


}

export const { sendMessage, onMessage } = defineExtensionMessaging<ProtocolMap>();
