import { onMessage, sendMessage } from '@/message'
import { create } from 'zustand'


interface State {
  account: AdqResult<Account | null> | null
  setAccount: (account: State['account']) => void
}

export const useStore = create<State>((set) => {
  sendMessage('adq_build_connect', {false: false}).then((data:any) => {
    set((state) => ({...state, account: data}))
  })

  return {
    account: null,
    setAccount: (account ) => {
      set((state) => ({...state, account}))
    },
  }
})

onMessage('adq_update_account', ({ data }:any) => {
  console.log('adq_update_account', data)
  useStore.getState().setAccount(data)
  return true
})

// onMessage('adq_update_log_port', (data:any) => {
//   console.log('adq_update_log_port', data)
//   // useLogStore.getState().setLog(data)
//   return true
// })


// 日志
// onMessage('adq_update_log_port', (data:any) => {
//   console.log(111, data)
//   set((state) => ({...state, log: data}))
//   return true
// })


